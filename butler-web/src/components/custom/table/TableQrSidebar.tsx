/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useEffect, useState, useRef } from "react";
import QRCode from "react-qr-code";
import { Button } from "@/components/ui/button";
import { Download, Share, Copy, ExternalLink } from "lucide-react";
import { toast } from "sonner";
import { getAdminFoodChainId } from "@/server";
import { handleTableQRDownload } from "./TableQrCode";

interface TableQrSidebarProps {
  outletId: string;
  tableId: string;
  tableName: string;
  outletName: string;
}

const TableQrSidebar: React.FC<TableQrSidebarProps> = ({
  outletId,
  tableId,
  tableName,
  outletName,
}) => {
  const [link, setLink] = useState("");
  const qrCodeRef = useRef(null);

  useEffect(() => {
    const chainId = getAdminFoodChainId();
    setLink(
      `${window.location.origin}/chat?${chainId ? `chainId=${chainId}` : ""}${
        outletId ? `&outletId=${outletId}` : ""
      }${tableId ? `&tableId=${tableId}` : ""}`
    );
  }, [outletId, tableId]);

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: `Table ${tableName} - ${outletName}`,
          text: `Scan this QR code to order from Table ${tableName}`,
          url: link,
        });
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(link);
        toast.success("Link copied to clipboard!");
      }
    } catch (error) {
      console.error("Error sharing:", error);
      toast.error("Failed to share");
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(link);
      toast.success("Link copied to clipboard!");
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      toast.error("Failed to copy link");
    }
  };

  const handleOpenLink = () => {
    window.open(link, "_blank");
  };

  return (
    <div className="space-y-6">
      {/* Table Info */}
      <div className="text-center">
        <h3 className="text-lg font-semibold">Table {tableName}</h3>
        <p className="text-sm text-gray-500">{outletName}</p>
      </div>

      {/* QR Code */}
      <div className="flex justify-center">
        <div className="p-4 bg-white rounded-lg shadow-sm border">
          <QRCode ref={qrCodeRef} value={link} size={200} />
        </div>
      </div>

      {/* Description */}
      <p className="text-sm text-gray-600 text-center">
        Scan this QR code to order from Table {tableName}
      </p>

      {/* Action Buttons */}
      <div className="space-y-3">
        <Button
          variant="outline"
          className="w-full"
          onClick={() => handleTableQRDownload(qrCodeRef, tableName, outletName)}
        >
          <Download className="h-4 w-4 mr-2" />
          Download QR Code
        </Button>
        
        <Button variant="outline" className="w-full" onClick={handleShare}>
          <Share className="h-4 w-4 mr-2" />
          Share Link
        </Button>
        
        <Button variant="outline" className="w-full" onClick={handleCopy}>
          <Copy className="h-4 w-4 mr-2" />
          Copy Link
        </Button>

        <Button variant="outline" className="w-full" onClick={handleOpenLink}>
          <ExternalLink className="h-4 w-4 mr-2" />
          Open in New Tab
        </Button>
      </div>

      {/* Link Preview */}
      <div className="p-3 bg-gray-50 rounded-md">
        <p className="text-xs text-gray-500 mb-1">Direct Link:</p>
        <p className="text-xs text-gray-700 break-all">{link}</p>
      </div>

      {/* Footer */}
      <div className="text-xs text-gray-400 text-center">
        Powered by Butler AI
      </div>
    </div>
  );
};

export default TableQrSidebar;
