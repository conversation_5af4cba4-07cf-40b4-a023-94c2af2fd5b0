"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";

interface Route {
  path: string;
  name: string;
  icon: string;
}

interface ResponsiveSidebarProps {
  routes: Route[];
  title: string;
  backgroundColor?: string;
  textColor?: string;
  children?: React.ReactNode;
}

export default function ResponsiveSidebar({
  routes,
  title,
  backgroundColor = "#1f2937", // Default to gray-800
  textColor = "#ffffff", // Default to white
  children,
}: ResponsiveSidebarProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };
  const [expand, setExpand] = useState(true);

  useEffect(() => {
    setExpand(localStorage.getItem("expand") === "true");
  }, []);

  return (
    <div className="relative">
      {/* Mobile menu button - only visible on small screens */}
      <div
        className="flex justify-between items-center p-4 sm:hidden"
        style={{ backgroundColor, color: textColor }}
      >
        <h1 className="font-bold text-xl">{title}</h1>
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleMenu}
          className="text-white hover:bg-opacity-20 hover:bg-white"
        >
          {isMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </Button>
      </div>

      {/* Sidebar - responsive behavior */}
      <div
        className={cn(
          "p-4 min-h-screen transition-all duration-300",
          isMenuOpen ? "block" : "hidden",
          "sm:block fixed sm:relative w-full z-20",
          expand ? "sm:w-64 md:w-72" : "sm:w-16"
        )}
        style={{ backgroundColor, color: textColor }}
      >
        {/* Desktop header - hidden on mobile */}
        <div className="hidden sm:flex items-center justify-between mb-6">
          {expand && <h2 className="text-xl font-bold">{title}</h2>}

          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              setExpand(!expand);
              localStorage.setItem("expand", !expand ? "true" : "false");
            }}
            className="text-white hover:bg-white/10"
          >
            <Icon
              icon={expand ? "mdi:chevron-left" : "mdi:chevron-right"}
              width="20"
              height="20"
            />
          </Button>
        </div>

        {/* Navigation links */}
        <ul className="space-y-2">
          {routes.map((route, index) => (
            <li key={index} className="relative group">
              <Link
                href={route.path}
                className={cn(
                  "flex items-center gap-3 p-2 rounded-md transition-colors",
                  "hover:bg-white/10"
                )}
                onClick={() => setIsMenuOpen(false)}
              >
                <Icon icon={route.icon} width={expand?"20":"24"} height={expand?"20":"24"} />

                {/* Text only when expanded */}
                {expand && <span>{route.name}</span>}
              </Link>

              {/* Tooltip when collapsed */}
              {!expand && (
                <span
                  className="
            absolute left-14 top-1/2 -translate-y-1/2
            whitespace-nowrap rounded-md bg-black px-2 py-1 text-sm text-white
            opacity-0 group-hover:opacity-100 transition-opacity
            pointer-events-none
          "
                >
                  {route.name}
                </span>
              )}
            </li>
          ))}
        </ul>

        {/* Additional content if provided */}
        {children && <div className="mt-6">{children}</div>}
        {isMenuOpen && (
          <div
            className=" md:hidden absolute z-50 bottom-20 p-4 flex justify-center items-center w-full"
            onClick={() => setIsMenuOpen(false)}
          >
            <Button variant="ghost" size="icon" className="bg-white">
              <X className="h-12 w-12 text-black" />
            </Button>
          </div>
        )}
      </div>

      {/* Overlay for mobile - closes menu when clicking outside */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-10 sm:hidden"
          onClick={() => setIsMenuOpen(false)}
        />
      )}
    </div>
  );
}
