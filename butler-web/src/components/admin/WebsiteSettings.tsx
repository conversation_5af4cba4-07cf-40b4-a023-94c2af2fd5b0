/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Image as ImageIcon,
  Star,
  HelpCircle,
  Share2,
  Layout,
  Save,
  X,
  Plus,
  Trash2,
  Globe,
  Bar<PERSON>hart,
  Tag,
  <PERSON>rkles,
} from "lucide-react";
import { toast } from "sonner";

interface WebsiteSettingsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  chain: any;
  onSave: (settings: any) => void;
}

const WebsiteSettings = ({ open, onOpenChange, chain, onSave }: WebsiteSettingsProps) => {
  const [settings, setSettings] = useState({
    // Page Configuration
    pageConfig: {
      enableHeroSection: true,
      enableAboutSection: true,
      enableOutletsSection: true,
      enableMenuSection: true,
      enableTestimonialsSection: false,
      enableGallerySection: false,
      enableFeaturedDishes: true,
      enableNewsSection: false,
      enableFAQSection: false,
      enableContactForm: false,
      enableSocialLinks: true,
      showStats: true,
      showRatings: true,
      showPricing: true,
      showCategories: true,
      showSearchBar: true,
      heroStyle: "gradient" as "gradient" | "image" | "video" | "minimal",
      menuLayout: "grid" as "grid" | "list" | "masonry",
    },

    // Hero Section
    heroSection: {
      enabled: true,
      title: chain?.name || "",
      subtitle: chain?.tagline || "",
      backgroundImage: "",
      backgroundVideo: "",
      ctaText: "Order Now",
      ctaLink: "",
      overlayOpacity: 0.3,
    },

    // About Section
    aboutSection: {
      enabled: true,
      title: "About Us",
      content: "",
      images: [] as string[],
      highlights: [] as Array<{ icon: string; title: string; description: string }>,
    },

    // Gallery
    gallery: {
      enabled: false,
      title: "Gallery",
      images: [] as Array<{ url: string; caption: string; category: string }>,
    },

    // Testimonials
    testimonials: {
      enabled: false,
      title: "What Our Customers Say",
      reviews: [] as Array<{
        customerName: string;
        customerImage: string;
        rating: number;
        review: string;
        featured: boolean;
      }>,
    },

    // Announcements
    announcements: [] as Array<{
      title: string;
      content: string;
      type: "promotion" | "event" | "news" | "alert";
      image: string;
      startDate: string;
      endDate: string;
      isActive: boolean;
      priority: number;
    }>,

    // Promotions
    promotions: [] as Array<{
      title: string;
      description: string;
      code: string;
      discountType: "percentage" | "fixed";
      discountValue: number;
      image: string;
      startDate: string;
      endDate: string;
      isActive: boolean;
    }>,

    // FAQs
    faqs: [] as Array<{
      question: string;
      answer: string;
      category: string;
      order: number;
    }>,

    // Social Media
    socialMedia: {
      facebook: "",
      instagram: "",
      twitter: "",
      youtube: "",
      linkedin: "",
      whatsapp: "",
    },

    // Features
    features: {
      onlineOrdering: true,
      tableReservation: false,
      homeDelivery: true,
      takeaway: true,
      dineIn: true,
      catering: false,
      vegetarianOptions: true,
      veganOptions: false,
      freeWifi: false,
      parking: false,
      outdoorSeating: false,
      liveMusic: false,
      kidsPlay: false,
    },

    // SEO
    seo: {
      metaTitle: "",
      metaDescription: "",
      metaKeywords: [] as string[],
      ogImage: "",
    },

    // Analytics
    analytics: {
      googleAnalyticsId: "",
      facebookPixelId: "",
      googleTagManagerId: "",
    },
  });

  const [newKeyword, setNewKeyword] = useState("");
  const [newHighlight, setNewHighlight] = useState({ icon: "", title: "", description: "" });
//   const [newGalleryImage, setNewGalleryImage] = useState({ url: "", caption: "", category: "" });
//   const [newTestimonial, setNewTestimonial] = useState({
//     customerName: "",
//     customerImage: "",
//     rating: 5,
//     review: "",
//     featured: false,
//   });
  const [newAnnouncement, setNewAnnouncement] = useState({
    title: "",
    content: "",
    type: "news" as "promotion" | "event" | "news" | "alert",
    image: "",
    startDate: new Date().toISOString().split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
    isActive: true,
    priority: 5,
  });
  const [newPromotion, setNewPromotion] = useState({
    title: "",
    description: "",
    code: "",
    discountType: "percentage" as "percentage" | "fixed",
    discountValue: 0,
    image: "",
    startDate: new Date().toISOString().split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
    isActive: true,
  });
  const [newFaq, setNewFaq] = useState({
    question: "",
    answer: "",
    category: "General",
    order: 0,
  });

  const handleSave = () => {
    // Validate required fields
    if (!settings.heroSection.title) {
      toast.error("Hero title is required");
      return;
    }

    onSave(settings);
    toast.success("Website settings saved successfully!");
    onOpenChange(false);
  };

  const addKeyword = () => {
    if (newKeyword && !settings.seo.metaKeywords.includes(newKeyword)) {
      setSettings({
        ...settings,
        seo: {
          ...settings.seo,
          metaKeywords: [...settings.seo.metaKeywords, newKeyword],
        },
      });
      setNewKeyword("");
    }
  };

  const removeKeyword = (keyword: string) => {
    setSettings({
      ...settings,
      seo: {
        ...settings.seo,
        metaKeywords: settings.seo.metaKeywords.filter((k) => k !== keyword),
      },
    });
  };

  const addHighlight = () => {
    if (newHighlight.title && newHighlight.description) {
      setSettings({
        ...settings,
        aboutSection: {
          ...settings.aboutSection,
          highlights: [...settings.aboutSection.highlights, newHighlight],
        },
      });
      setNewHighlight({ icon: "", title: "", description: "" });
    }
  };

//   const addGalleryImage = () => {
//     if (newGalleryImage.url) {
//       setSettings({
//         ...settings,
//         gallery: {
//           ...settings.gallery,
//           images: [...settings.gallery.images, newGalleryImage],
//         },
//       });
//       setNewGalleryImage({ url: "", caption: "", category: "" });
//     }
//   };

//   const addTestimonial = () => {
//     if (newTestimonial.customerName && newTestimonial.review) {
//       setSettings({
//         ...settings,
//         testimonials: {
//           ...settings.testimonials,
//           reviews: [...settings.testimonials.reviews, newTestimonial],
//         },
//       });
//       setNewTestimonial({
//         customerName: "",
//         customerImage: "",
//         rating: 5,
//         review: "",
//         featured: false,
//       });
//     }
//   };

  const addAnnouncement = () => {
    if (newAnnouncement.title && newAnnouncement.content) {
      setSettings({
        ...settings,
        announcements: [...settings.announcements, newAnnouncement],
      });
      setNewAnnouncement({
        title: "",
        content: "",
        type: "news",
        image: "",
        startDate: new Date().toISOString().split("T")[0],
        endDate: new Date().toISOString().split("T")[0],
        isActive: true,
        priority: 5,
      });
    }
  };

  const addPromotion = () => {
    if (newPromotion.title && newPromotion.description) {
      setSettings({
        ...settings,
        promotions: [...settings.promotions, newPromotion],
      });
      setNewPromotion({
        title: "",
        description: "",
        code: "",
        discountType: "percentage",
        discountValue: 0,
        image: "",
        startDate: new Date().toISOString().split("T")[0],
        endDate: new Date().toISOString().split("T")[0],
        isActive: true,
      });
    }
  };

  const addFaq = () => {
    if (newFaq.question && newFaq.answer) {
      setSettings({
        ...settings,
        faqs: [...settings.faqs, newFaq],
      });
      setNewFaq({
        question: "",
        answer: "",
        category: "General",
        order: settings.faqs.length,
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Website Settings
          </DialogTitle>
          <DialogDescription>
            Configure your public-facing website. Changes will be visible immediately to customers.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="general" className="text-xs">
              <Layout className="h-4 w-4 mr-1" />
              General
            </TabsTrigger>
            <TabsTrigger value="content" className="text-xs">
              <ImageIcon className="h-4 w-4 mr-1" />
              Content
            </TabsTrigger>
            <TabsTrigger value="features" className="text-xs">
              <Star className="h-4 w-4 mr-1" />
              Features
            </TabsTrigger>
            <TabsTrigger value="social" className="text-xs">
              <Share2 className="h-4 w-4 mr-1" />
              Social
            </TabsTrigger>
            <TabsTrigger value="seo" className="text-xs">
              <BarChart className="h-4 w-4 mr-1" />
              SEO
            </TabsTrigger>
          </TabsList>

          {/* General Tab */}
          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Page Sections</CardTitle>
                <CardDescription>Toggle which sections appear on your website</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="hero" className="text-sm">Hero Section</Label>
                    <Switch
                      id="hero"
                      checked={settings.pageConfig.enableHeroSection}
                      onCheckedChange={(checked) =>
                        setSettings({
                          ...settings,
                          pageConfig: { ...settings.pageConfig, enableHeroSection: checked },
                        })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="about" className="text-sm">About Section</Label>
                    <Switch
                      id="about"
                      checked={settings.pageConfig.enableAboutSection}
                      onCheckedChange={(checked) =>
                        setSettings({
                          ...settings,
                          pageConfig: { ...settings.pageConfig, enableAboutSection: checked },
                        })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="menu" className="text-sm">Menu Section</Label>
                    <Switch
                      id="menu"
                      checked={settings.pageConfig.enableMenuSection}
                      onCheckedChange={(checked) =>
                        setSettings({
                          ...settings,
                          pageConfig: { ...settings.pageConfig, enableMenuSection: checked },
                        })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="outlets" className="text-sm">Outlets Section</Label>
                    <Switch
                      id="outlets"
                      checked={settings.pageConfig.enableOutletsSection}
                      onCheckedChange={(checked) =>
                        setSettings({
                          ...settings,
                          pageConfig: { ...settings.pageConfig, enableOutletsSection: checked },
                        })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="testimonials" className="text-sm">Testimonials</Label>
                    <Switch
                      id="testimonials"
                      checked={settings.pageConfig.enableTestimonialsSection}
                      onCheckedChange={(checked) =>
                        setSettings({
                          ...settings,
                          pageConfig: { ...settings.pageConfig, enableTestimonialsSection: checked },
                        })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="gallery" className="text-sm">Gallery</Label>
                    <Switch
                      id="gallery"
                      checked={settings.pageConfig.enableGallerySection}
                      onCheckedChange={(checked) =>
                        setSettings({
                          ...settings,
                          pageConfig: { ...settings.pageConfig, enableGallerySection: checked },
                        })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="featured" className="text-sm">Featured Dishes</Label>
                    <Switch
                      id="featured"
                      checked={settings.pageConfig.enableFeaturedDishes}
                      onCheckedChange={(checked) =>
                        setSettings({
                          ...settings,
                          pageConfig: { ...settings.pageConfig, enableFeaturedDishes: checked },
                        })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="news" className="text-sm">Announcements</Label>
                    <Switch
                      id="news"
                      checked={settings.pageConfig.enableNewsSection}
                      onCheckedChange={(checked) =>
                        setSettings({
                          ...settings,
                          pageConfig: { ...settings.pageConfig, enableNewsSection: checked },
                        })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between space-x-2">
                    <Label htmlFor="faq" className="text-sm">FAQ Section</Label>
                    <Switch
                      id="faq"
                      checked={settings.pageConfig.enableFAQSection}
                      onCheckedChange={(checked) =>
                        setSettings({
                          ...settings,
                          pageConfig: { ...settings.pageConfig, enableFAQSection: checked },
                        })
                      }
                    />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="text-sm font-semibold">Display Options</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="stats" className="text-sm">Show Statistics</Label>
                      <Switch
                        id="stats"
                        checked={settings.pageConfig.showStats}
                        onCheckedChange={(checked) =>
                          setSettings({
                            ...settings,
                            pageConfig: { ...settings.pageConfig, showStats: checked },
                          })
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="ratings" className="text-sm">Show Ratings</Label>
                      <Switch
                        id="ratings"
                        checked={settings.pageConfig.showRatings}
                        onCheckedChange={(checked) =>
                          setSettings({
                            ...settings,
                            pageConfig: { ...settings.pageConfig, showRatings: checked },
                          })
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="pricing" className="text-sm">Show Pricing</Label>
                      <Switch
                        id="pricing"
                        checked={settings.pageConfig.showPricing}
                        onCheckedChange={(checked) =>
                          setSettings({
                            ...settings,
                            pageConfig: { ...settings.pageConfig, showPricing: checked },
                          })
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="search" className="text-sm">Show Search Bar</Label>
                      <Switch
                        id="search"
                        checked={settings.pageConfig.showSearchBar}
                        onCheckedChange={(checked) =>
                          setSettings({
                            ...settings,
                            pageConfig: { ...settings.pageConfig, showSearchBar: checked },
                          })
                        }
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="grid gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="heroStyle">Hero Style</Label>
                      <Select
                        value={settings.pageConfig.heroStyle}
                        onValueChange={(value: any) =>
                          setSettings({
                            ...settings,
                            pageConfig: { ...settings.pageConfig, heroStyle: value },
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="gradient">Gradient</SelectItem>
                          <SelectItem value="image">Background Image</SelectItem>
                          <SelectItem value="video">Background Video</SelectItem>
                          <SelectItem value="minimal">Minimal</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="menuLayout">Menu Layout</Label>
                      <Select
                        value={settings.pageConfig.menuLayout}
                        onValueChange={(value: any) =>
                          setSettings({
                            ...settings,
                            pageConfig: { ...settings.pageConfig, menuLayout: value },
                          })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="grid">Grid (Cards)</SelectItem>
                          <SelectItem value="list">List</SelectItem>
                          <SelectItem value="masonry">Masonry</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Hero Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Hero Section</CardTitle>
                <CardDescription>Customize your main banner</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="heroTitle">Title</Label>
                  <Input
                    id="heroTitle"
                    value={settings.heroSection.title}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        heroSection: { ...settings.heroSection, title: e.target.value },
                      })
                    }
                    placeholder="Your restaurant name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="heroSubtitle">Subtitle</Label>
                  <Input
                    id="heroSubtitle"
                    value={settings.heroSection.subtitle}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        heroSection: { ...settings.heroSection, subtitle: e.target.value },
                      })
                    }
                    placeholder="Your tagline"
                  />
                </div>

                {settings.pageConfig.heroStyle === "image" && (
                  <div className="space-y-2">
                    <Label htmlFor="heroBgImage">Background Image URL</Label>
                    <Input
                      id="heroBgImage"
                      value={settings.heroSection.backgroundImage}
                      onChange={(e) =>
                        setSettings({
                          ...settings,
                          heroSection: { ...settings.heroSection, backgroundImage: e.target.value },
                        })
                      }
                      placeholder="https://example.com/hero.jpg"
                    />
                  </div>
                )}

                {settings.pageConfig.heroStyle === "video" && (
                  <div className="space-y-2">
                    <Label htmlFor="heroBgVideo">Background Video URL</Label>
                    <Input
                      id="heroBgVideo"
                      value={settings.heroSection.backgroundVideo}
                      onChange={(e) =>
                        setSettings({
                          ...settings,
                          heroSection: { ...settings.heroSection, backgroundVideo: e.target.value },
                        })
                      }
                      placeholder="https://example.com/hero.mp4"
                    />
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="heroCta">CTA Button Text</Label>
                    <Input
                      id="heroCta"
                      value={settings.heroSection.ctaText}
                      onChange={(e) =>
                        setSettings({
                          ...settings,
                          heroSection: { ...settings.heroSection, ctaText: e.target.value },
                        })
                      }
                      placeholder="Order Now"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="heroCtaLink">CTA Link (optional)</Label>
                    <Input
                      id="heroCtaLink"
                      value={settings.heroSection.ctaLink}
                      onChange={(e) =>
                        setSettings({
                          ...settings,
                          heroSection: { ...settings.heroSection, ctaLink: e.target.value },
                        })
                      }
                      placeholder="#menu"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="overlayOpacity">
                    Overlay Opacity: {Math.round(settings.heroSection.overlayOpacity * 100)}%
                  </Label>
                  <input
                    type="range"
                    id="overlayOpacity"
                    min="0"
                    max="1"
                    step="0.1"
                    value={settings.heroSection.overlayOpacity}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        heroSection: {
                          ...settings.heroSection,
                          overlayOpacity: parseFloat(e.target.value),
                        },
                      })
                    }
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Content Tab */}
          <TabsContent value="content" className="space-y-6">
            {/* About Section */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">About Section</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="aboutTitle">Section Title</Label>
                  <Input
                    id="aboutTitle"
                    value={settings.aboutSection.title}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        aboutSection: { ...settings.aboutSection, title: e.target.value },
                      })
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="aboutContent">Content</Label>
                  <Textarea
                    id="aboutContent"
                    value={settings.aboutSection.content}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        aboutSection: { ...settings.aboutSection, content: e.target.value },
                      })
                    }
                    rows={5}
                    placeholder="Tell your story..."
                  />
                </div>

                <Separator />

                <div className="space-y-4">
                  <Label>Highlights</Label>
                  <div className="grid gap-2">
                    <div className="flex gap-2">
                      <Input
                        placeholder="Title"
                        value={newHighlight.title}
                        onChange={(e) => setNewHighlight({ ...newHighlight, title: e.target.value })}
                      />
                      <Input
                        placeholder="Description"
                        value={newHighlight.description}
                        onChange={(e) =>
                          setNewHighlight({ ...newHighlight, description: e.target.value })
                        }
                      />
                      <Button onClick={addHighlight} size="icon">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {settings.aboutSection.highlights.map((highlight, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div>
                        <div className="font-semibold">{highlight.title}</div>
                        <div className="text-sm text-muted-foreground">{highlight.description}</div>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() =>
                          setSettings({
                            ...settings,
                            aboutSection: {
                              ...settings.aboutSection,
                              highlights: settings.aboutSection.highlights.filter((_, i) => i !== index),
                            },
                          })
                        }
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Announcements */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  Announcements
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-3">
                  <Input
                    placeholder="Title"
                    value={newAnnouncement.title}
                    onChange={(e) => setNewAnnouncement({ ...newAnnouncement, title: e.target.value })}
                  />
                  <Textarea
                    placeholder="Content"
                    value={newAnnouncement.content}
                    onChange={(e) => setNewAnnouncement({ ...newAnnouncement, content: e.target.value })}
                  />
                  <div className="grid grid-cols-2 gap-2">
                    <Select
                      value={newAnnouncement.type}
                      onValueChange={(value: any) =>
                        setNewAnnouncement({ ...newAnnouncement, type: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="news">News</SelectItem>
                        <SelectItem value="promotion">Promotion</SelectItem>
                        <SelectItem value="event">Event</SelectItem>
                        <SelectItem value="alert">Alert</SelectItem>
                      </SelectContent>
                    </Select>
                    <Input
                      type="number"
                      placeholder="Priority (1-10)"
                      value={newAnnouncement.priority}
                      onChange={(e) =>
                        setNewAnnouncement({ ...newAnnouncement, priority: parseInt(e.target.value) })
                      }
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label className="text-xs">Start Date</Label>
                      <Input
                        type="date"
                        value={newAnnouncement.startDate}
                        onChange={(e) =>
                          setNewAnnouncement({ ...newAnnouncement, startDate: e.target.value })
                        }
                      />
                    </div>
                    <div className="space-y-1">
                      <Label className="text-xs">End Date</Label>
                      <Input
                        type="date"
                        value={newAnnouncement.endDate}
                        onChange={(e) =>
                          setNewAnnouncement({ ...newAnnouncement, endDate: e.target.value })
                        }
                      />
                    </div>
                  </div>
                  <Button onClick={addAnnouncement}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Announcement
                  </Button>
                </div>

                {settings.announcements.map((announcement, index) => (
                  <div key={index} className="flex items-start justify-between p-3 bg-muted rounded-lg">
                    <div>
                      <div className="font-semibold flex items-center gap-2">
                        {announcement.title}
                        <Badge variant="outline">{announcement.type}</Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">{announcement.content}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {announcement.startDate} to {announcement.endDate}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() =>
                        setSettings({
                          ...settings,
                          announcements: settings.announcements.filter((_, i) => i !== index),
                        })
                      }
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Promotions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Tag className="h-5 w-5" />
                  Promotions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-3">
                  <Input
                    placeholder="Title"
                    value={newPromotion.title}
                    onChange={(e) => setNewPromotion({ ...newPromotion, title: e.target.value })}
                  />
                  <Textarea
                    placeholder="Description"
                    value={newPromotion.description}
                    onChange={(e) => setNewPromotion({ ...newPromotion, description: e.target.value })}
                  />
                  <div className="grid grid-cols-3 gap-2">
                    <Input
                      placeholder="Promo Code"
                      value={newPromotion.code}
                      onChange={(e) => setNewPromotion({ ...newPromotion, code: e.target.value })}
                    />
                    <Select
                      value={newPromotion.discountType}
                      onValueChange={(value: any) =>
                        setNewPromotion({ ...newPromotion, discountType: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="percentage">Percentage</SelectItem>
                        <SelectItem value="fixed">Fixed Amount</SelectItem>
                      </SelectContent>
                    </Select>
                    <Input
                      type="number"
                      placeholder="Value"
                      value={newPromotion.discountValue}
                      onChange={(e) =>
                        setNewPromotion({ ...newPromotion, discountValue: parseInt(e.target.value) })
                      }
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-1">
                      <Label className="text-xs">Start Date</Label>
                      <Input
                        type="date"
                        value={newPromotion.startDate}
                        onChange={(e) => setNewPromotion({ ...newPromotion, startDate: e.target.value })}
                      />
                    </div>
                    <div className="space-y-1">
                      <Label className="text-xs">End Date</Label>
                      <Input
                        type="date"
                        value={newPromotion.endDate}
                        onChange={(e) => setNewPromotion({ ...newPromotion, endDate: e.target.value })}
                      />
                    </div>
                  </div>
                  <Button onClick={addPromotion}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Promotion
                  </Button>
                </div>

                {settings.promotions.map((promo, index) => (
                  <div key={index} className="flex items-start justify-between p-3 bg-muted rounded-lg">
                    <div>
                      <div className="font-semibold flex items-center gap-2">
                        {promo.title}
                        {promo.code && <Badge>{promo.code}</Badge>}
                      </div>
                      <div className="text-sm text-muted-foreground">{promo.description}</div>
                      <div className="text-sm font-semibold mt-1">
                        {promo.discountType === "percentage" ? `${promo.discountValue}%` : `₹${promo.discountValue}`} off
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() =>
                        setSettings({
                          ...settings,
                          promotions: settings.promotions.filter((_, i) => i !== index),
                        })
                      }
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* FAQs */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <HelpCircle className="h-5 w-5" />
                  FAQs
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-3">
                  <Input
                    placeholder="Question"
                    value={newFaq.question}
                    onChange={(e) => setNewFaq({ ...newFaq, question: e.target.value })}
                  />
                  <Textarea
                    placeholder="Answer"
                    value={newFaq.answer}
                    onChange={(e) => setNewFaq({ ...newFaq, answer: e.target.value })}
                  />
                  <Input
                    placeholder="Category (optional)"
                    value={newFaq.category}
                    onChange={(e) => setNewFaq({ ...newFaq, category: e.target.value })}
                  />
                  <Button onClick={addFaq}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add FAQ
                  </Button>
                </div>

                {settings.faqs.map((faq, index) => (
                  <div key={index} className="flex items-start justify-between p-3 bg-muted rounded-lg">
                    <div>
                      <div className="font-semibold">{faq.question}</div>
                      <div className="text-sm text-muted-foreground">{faq.answer}</div>
                      {faq.category && <Badge variant="outline" className="mt-1">{faq.category}</Badge>}
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() =>
                        setSettings({
                          ...settings,
                          faqs: settings.faqs.filter((_, i) => i !== index),
                        })
                      }
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Features Tab */}
          <TabsContent value="features" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Services & Amenities</CardTitle>
                <CardDescription>Select the features available at your restaurant</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(settings.features).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between space-x-2">
                      <Label htmlFor={key} className="text-sm capitalize">
                        {key.replace(/([A-Z])/g, " $1").trim()}
                      </Label>
                      <Switch
                        id={key}
                        checked={value as boolean}
                        onCheckedChange={(checked) =>
                          setSettings({
                            ...settings,
                            features: { ...settings.features, [key]: checked },
                          })
                        }
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Social Tab */}
          <TabsContent value="social" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Social Media Links</CardTitle>
                <CardDescription>Connect your social media profiles</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(settings.socialMedia).map(([platform, url]) => (
                  <div key={platform} className="space-y-2">
                    <Label htmlFor={platform} className="capitalize">
                      {platform}
                    </Label>
                    <Input
                      id={platform}
                      value={url}
                      onChange={(e) =>
                        setSettings({
                          ...settings,
                          socialMedia: { ...settings.socialMedia, [platform]: e.target.value },
                        })
                      }
                      placeholder={`https://${platform}.com/yourpage`}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          {/* SEO Tab */}
          <TabsContent value="seo" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">SEO Settings</CardTitle>
                <CardDescription>Optimize your website for search engines</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="metaTitle">Meta Title</Label>
                  <Input
                    id="metaTitle"
                    value={settings.seo.metaTitle}
                    onChange={(e) =>
                      setSettings({ ...settings, seo: { ...settings.seo, metaTitle: e.target.value } })
                    }
                    placeholder="Best Restaurant in City | Your Restaurant Name"
                    maxLength={60}
                  />
                  <p className="text-xs text-muted-foreground">
                    {settings.seo.metaTitle.length}/60 characters
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="metaDescription">Meta Description</Label>
                  <Textarea
                    id="metaDescription"
                    value={settings.seo.metaDescription}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        seo: { ...settings.seo, metaDescription: e.target.value },
                      })
                    }
                    placeholder="Discover delicious food at our restaurant..."
                    maxLength={160}
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    {settings.seo.metaDescription.length}/160 characters
                  </p>
                </div>

                <div className="space-y-2">
                  <Label>Keywords</Label>
                  <div className="flex gap-2">
                    <Input
                      value={newKeyword}
                      onChange={(e) => setNewKeyword(e.target.value)}
                      placeholder="Add keyword"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          addKeyword();
                        }
                      }}
                    />
                    <Button onClick={addKeyword} size="icon">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {settings.seo.metaKeywords.map((keyword) => (
                      <Badge key={keyword} variant="secondary" className="cursor-pointer">
                        {keyword}
                        <X
                          className="h-3 w-3 ml-1"
                          onClick={() => removeKeyword(keyword)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ogImage">Open Graph Image URL</Label>
                  <Input
                    id="ogImage"
                    value={settings.seo.ogImage}
                    onChange={(e) =>
                      setSettings({ ...settings, seo: { ...settings.seo, ogImage: e.target.value } })
                    }
                    placeholder="https://example.com/og-image.jpg"
                  />
                  <p className="text-xs text-muted-foreground">
                    Recommended size: 1200x630px
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Analytics</CardTitle>
                <CardDescription>Track your website performance</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="ga">Google Analytics ID</Label>
                  <Input
                    id="ga"
                    value={settings.analytics.googleAnalyticsId}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        analytics: { ...settings.analytics, googleAnalyticsId: e.target.value },
                      })
                    }
                    placeholder="G-XXXXXXXXXX"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fbPixel">Facebook Pixel ID</Label>
                  <Input
                    id="fbPixel"
                    value={settings.analytics.facebookPixelId}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        analytics: { ...settings.analytics, facebookPixelId: e.target.value },
                      })
                    }
                    placeholder="123456789012345"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gtm">Google Tag Manager ID</Label>
                  <Input
                    id="gtm"
                    value={settings.analytics.googleTagManagerId}
                    onChange={(e) =>
                      setSettings({
                        ...settings,
                        analytics: { ...settings.analytics, googleTagManagerId: e.target.value },
                      })
                    }
                    placeholder="GTM-XXXXXXX"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Save Settings
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default WebsiteSettings;