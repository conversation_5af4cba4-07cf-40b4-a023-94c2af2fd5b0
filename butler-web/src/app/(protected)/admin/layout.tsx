"use client";
import { useTheme } from "@/contexts/ThemeContext";
import { getRoutes } from "@/routes";
import { Toaster } from "sonner";
import NotificationBell from "@/components/notifications/NotificationBell";
import { UserButton } from "@/components/UserButton";
import ResponsiveSidebar from "@/components/layouts/ResponsiveSidebar";
import GlobalOrderNotifications from "@/components/notifications/GlobalOrderNotifications";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const routes = getRoutes("admin");
  const { theme } = useTheme();

  return (
    <div className="min-h-screen flex flex-col sm:flex-row">
      {/* Admin sidebar */}
      <ResponsiveSidebar
        routes={routes}
        title={theme.name}
        backgroundColor={theme.primaryColor}
        textColor="#ffffff"
      />
      {/* Main content */}
      <div className="h-screen overflow-y-auto w-full">
        <div className="flex-1 flex flex-col min-h-screen overflow-y-auto">
          <header className="flex justify-center items-center ">
            <div className="relative hidden md:block">
              <div className="fixed top-0 border-2 border-gray-600 bg-white z-[999] border-t-0 rounded-t-none rounded-xl  flex items-center gap-2 p-1">
                <NotificationBell />
                <UserButton />
              </div>
            </div>
          </header>
          <main className="flex-1 p-1">{children}</main>
        </div>
      </div>
      <Toaster />
      <GlobalOrderNotifications />
    </div>
  );
}
