/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useOrderSocket } from "@/hooks/useOrderSocket";
import { useState, useEffect, useMemo, Suspense } from "react";
import { getAllOutlets, getAllEmployees } from "@/server/admin";
import { Outlet, User } from "@/app/type";
import { toast } from "sonner";
import PaymentRequestButton from "@/components/payment/PaymentRequestButton";
import PaymentLinkDisplay from "@/components/payment/PaymentLinkDisplay";
import { Payment, Order } from "@/types/order";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import OrderManagementDialog from "@/components/custom/orders/OrderManagementDialog";
import CreateOrderDialog from "@/components/custom/orders/CreateOrderDialog";
import FeedbackViewDialog from "@/components/admin/FeedbackViewDialog";
import MiscellaneousAmountDialog from "@/components/admin/MiscellaneousAmountDialog";
import AdminOrderUpdateDialog from "@/components/admin/AdminOrderUpdateDialog";
import OrderUpdateNotifications from "@/components/admin/OrderUpdateNotifications";
import {
  Clock,
  PlusCircle,
  Plus,
  Trash2,
  FileDown,
  Phone,
  User as UserIcon,
  Check,
  X,
  CheckCircle,
  Mail,
  ChevronDown,
  Edit,
  MessageSquare,
  Package,
  CheckCircle2,
  Truck,
  MoreHorizontal,
  Calendar,
} from "lucide-react";
import { downloadOrderBill, emailOrderInvoice } from "@/utils/pdfGenerator";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  deleteOrder,
  updateOrderPaymentStatus,
  markDishAsServed,
} from "@/server/admin";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import OrderSearch from "@/components/admin/OrderSearch";
import { useRouter, useSearchParams } from "next/navigation";

const orderStatuses = [
  {
    status: "pending",
    order: 1,
    icon: Clock,
    color: "from-amber-400 to-orange-500",
    borderClass: "border-amber-400",
    bgClass: "bg-amber-500",
    textClass: "text-amber-500",
    ringClass: "ring-amber-500",
    shadowClass: "shadow-amber-500/30",
    progressColor: "from-amber-400 to-orange-500",
  },
  {
    status: "preparing",
    order: 2,
    icon: Package,
    color: "from-blue-400 to-indigo-500",
    borderClass: "border-blue-400",
    bgClass: "bg-blue-500",
    textClass: "text-blue-500",
    ringClass: "ring-blue-500",
    shadowClass: "shadow-blue-500/30",
    progressColor: "from-amber-400 to-indigo-500",
  },
  {
    status: "ready",
    order: 3,
    icon: Truck,
    color: "from-purple-400 to-pink-500",
    borderClass: "border-purple-400",
    bgClass: "bg-purple-500",
    textClass: "text-purple-500",
    ringClass: "ring-purple-500",
    shadowClass: "shadow-purple-500/30",
    progressColor: "from-amber-400 via-indigo-500 to-purple-500",
  },
  {
    status: "completed",
    order: 4,
    icon: CheckCircle2,
    color: "from-emerald-400 to-green-500",
    borderClass: "border-emerald-500",
    bgClass: "bg-emerald-500",
    textClass: "text-emerald-500",
    ringClass: "ring-emerald-500",
    shadowClass: "shadow-emerald-500/30",
    progressColor: "from-amber-400 via-indigo-500 via-purple-500 to-green-500",
  },
];

const prioritiesStyle = {
  low: "bg-blue-100 text-blue-800 border-blue-300 border-2 border-blue-300",
  normal:
    "bg-green-100 text-green-800 border-green-300 border-2 border-green-300",
  high: "bg-orange-200 text-orange-800 border-orange-300 border-2 border-orange-300",
  urgent: "bg-red-100 text-red-800 border-red-300 border-2 border-red-300",
};

const AdminOrdersPage = () => {
  const {
    orders: socketOrders,
    loading: socketLoading,
    socket,
    updateOrderOptimistically,
    refreshOrders,
  } = useOrderSocket();
  const router = useRouter();
  const params = useSearchParams();
  const initialStatus = params?.get("status") || "pending";
  const [selectedStatus, setSelectedStatus] = useState<string>(initialStatus);
  const [selectedOutlet, setSelectedOutlet] = useState<string>("all");
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [payments, setPayments] = useState<Record<string, Payment>>({});
  const [staff, setStaff] = useState<User[]>([]);
  const [orderAnimate, setOrderAnimate] = useState("");
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderManagement, setShowOrderManagement] =
    useState<boolean>(false);
  const [showCreateOrder, setShowCreateOrder] = useState<boolean>(false);
  const [showOrderUpdate, setShowOrderUpdate] = useState<boolean>(false);
  const [orderToUpdate, setOrderToUpdate] = useState<Order | null>(null);
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);
  const [selectedOrderForFeedback, setSelectedOrderForFeedback] =
    useState<boolean>(false);
  const [showMiscAmountDialog, setShowMiscAmountDialog] = useState(false);
  const [selectedOrderForMiscAmount, setSelectedOrderForMiscAmount] =
    useState<Order | null>(null);
  const [highlightedOrderId, setHighlightedOrderId] = useState<string | null>(
    null
  );
  const [updatingDishes, setUpdatingDishes] = useState<Set<string>>(new Set());
  const [emailingInvoices, setEmailingInvoices] = useState<Set<string>>(
    new Set()
  );
  // Pagination state
  const [allOrders, setAllOrders] = useState<Order[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    hasNext: false,
    total: 0,
  });
  const [loadingMore, setLoadingMore] = useState(false);
  const [useSocketOrders, setUseSocketOrders] = useState(true);
  // Fetch orders from API with pagination
  const fetchOrdersFromAPI = async (page = 1, append = false) => {
    try {
      if (append) {
        setLoadingMore(true);
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/orders/paginated?page=${page}&limit=${pagination.limit}&status=${selectedStatus}&outlet=${selectedOutlet}`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch orders");
      }

      const data = await response.json();

      if (data.success) {
        if (append) {
          setAllOrders((prev) => [...prev, ...data.data]);
        } else {
          setAllOrders(data.data);
        }
        setPagination(data.pagination);
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      toast.error("Failed to load orders");
    } finally {
      setLoadingMore(false);
    }
  };

  // Load more orders
  const loadMoreOrders = () => {
    if (pagination.hasNext && !loadingMore) {
      fetchOrdersFromAPI(pagination.page + 1, true);
    }
  };

  // Get current orders based on mode
  const orders = useSocketOrders ? socketOrders : allOrders;
  const loading = useSocketOrders ? socketLoading : false;

  // Memoized filtered orders to ensure proper reactivity to socket updates
  const filteredOrders = useMemo(() => {
    const filtered = orders.filter(
      (order) =>
        (selectedStatus === "all" || order.status === selectedStatus) &&
        (selectedOutlet === "all" || order.outletId._id === selectedOutlet)
    );

    return filtered;
  }, [orders, selectedStatus, selectedOutlet]);

  // Handle email invoice
  const handleEmailInvoice = async (order: Order) => {
    if (!order.userId.email) {
      toast.error("Customer email not available");
      return;
    }

    setEmailingInvoices((prev) => new Set(prev).add(order._id));

    try {
      await emailOrderInvoice(order);
      toast.success(`Invoice emailed to ${order.userId.email}`);
    } catch (error) {
      console.error("Error sending email invoice:", error);
      toast.error("Failed to send invoice email");
    } finally {
      setEmailingInvoices((prev) => {
        const newSet = new Set(prev);
        newSet.delete(order._id);
        return newSet;
      });
    }
  };

  // Handle dish serving status changes
  const handleDishServingToggle = async (
    orderId: string,
    itemIndex: number,
    currentStatus: boolean
  ) => {
    const dishKey = `${orderId}-${itemIndex}`;
    const newStatus = !currentStatus;
    setUpdatingDishes((prev) => new Set(prev).add(dishKey));

    try {
      // Optimistic update - immediately update the UI
      if (useSocketOrders && updateOrderOptimistically) {
        // For socket orders, use optimistic update function
        updateOrderOptimistically(orderId, {
          [`items.${itemIndex}.isServed`]: newStatus,
          [`items.${itemIndex}.servedAt`]: newStatus
            ? new Date().toISOString()
            : null,
        });
      } else {
        // For API orders, update local state
        setAllOrders((prevOrders) =>
          prevOrders.map((order) => {
            if (order._id === orderId) {
              const updatedItems = [...order.items];
              updatedItems[itemIndex] = {
                ...updatedItems[itemIndex],
                isServed: newStatus,
                servedAt: newStatus ? new Date().toISOString() : undefined,
              };
              return { ...order, items: updatedItems };
            }
            return order;
          })
        );
      }

      const response = await markDishAsServed(orderId, itemIndex, newStatus);
      if (response.success) {
        toast.success(
          newStatus ? "Dish marked as served" : "Dish marked as not served"
        );
        // The socket will provide the authoritative update
      } else {
        toast.error("Failed to update dish status");
        // Revert optimistic update on error
        if (socket) {
          socket.emit("refresh-orders");
        }
      }
    } catch (error) {
      console.error("Error updating dish status:", error);
      toast.error("Failed to update dish status");
      // Revert optimistic update on error
      if (socket) {
        socket.emit("refresh-orders");
      }
    } finally {
      setUpdatingDishes((prev) => {
        const newSet = new Set(prev);
        newSet.delete(dishKey);
        return newSet;
      });
    }
  };

  const getOrderStatusNumber = (status: string) => {
    switch (status) {
      case "pending":
        return 1;
      case "preparing":
        return 2;
      case "ready":
        return 3;
      case "completed":
        return 4;
      default:
        return 0;
    }
  };

  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const [outletsResponse, staffResponse] = await Promise.all([
          getAllOutlets(),
          getAllEmployees(),
        ]);

        setOutlets(outletsResponse.data);
        if (outletsResponse.data.length == 1) {
          setSelectedOutlet(outletsResponse.data[0]._id);
        }
        if (staffResponse.success) {
          setStaff(staffResponse.data);
        } else {
          toast.error("Failed to fetch staff");
        }

        // Try to use API for pagination, fallback to WebSocket
        if (!useSocketOrders) {
          fetchOrdersFromAPI(1, false);
        } else {
          // We're now relying solely on WebSockets for order data
          // The API endpoint for orders is not available
          if (loading) {
            // After 5 seconds, if still loading, show a message and try to refresh
            setTimeout(() => {
              if (orders.length === 0 && socket) {
                socket.emit("refresh-orders");
              }
            }, 5000);
          }
        }
      } catch (error) {
        console.error("Error fetching initial data:", error);
      }
    };

    fetchInitialData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedStatus, selectedOutlet, useSocketOrders]);

  // Separate effect for socket orders with improved reactivity
  useEffect(() => {
    if (useSocketOrders && socketOrders.length > 0) {
      // When using socket orders, check if we need to switch to API for pagination
      if (socketOrders.length >= 50) {
        // If we have 50 orders (the socket limit), switch to API mode for pagination
        setUseSocketOrders(false);
        setAllOrders(socketOrders);
        setPagination((prev) => ({
          ...prev,
          hasNext: true,
          total: socketOrders.length,
        }));
      }
    }
  }, [socketOrders, useSocketOrders, socketLoading]);

  // Force re-render when socket orders change to ensure UI updates
  useEffect(() => {
    if (useSocketOrders) {
      // This effect ensures the component re-renders when socket orders change
      // The dependency on socketOrders will trigger this effect
    }
  }, [socketOrders, useSocketOrders, filteredOrders.length]);

  const formatOutletDisplay = (outlet: Outlet) => {
    return `${outlet.name} (${outlet.address.split(",")[0]})`;
  };

  useEffect(() => {
    if (orders.length > 0) {
      // Fetch payment information for all orders
      orders.forEach(async (order) => {
        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/orders/${order._id}/payment`,
            {
              headers: {
                Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
              },
            }
          );

          const data = await response.json();
          if (data.success && data.data.payment) {
            setPayments((prev) => ({
              ...prev,
              [order._id]: data.data.payment,
            }));
          }
        } catch (error) {
          console.error("Error fetching payment details:", error);
        }
      });
    }
  }, [orders]);
  // Socket is already defined above

  useEffect(() => {
    if (socket) {
      // Listen for payment updates to update payment information
      socket.on(
        "payment-update",
        (data: { type: string; data: { order: any; payment: Payment } }) => {
          // Update the payment information
          if (data.data.payment) {
            setPayments((prev) => ({
              ...prev,
              [data.data.order._id]: data.data.payment,
            }));
          }

          // Show success toast
          if (data.data.order) {
            toast.success(
              `Payment status updated to ${data.data.order.paymentStatus}`
            );
          }
        }
      );

      return () => {
        socket.off("payment-update");
      };
    }
  }, [socket]);

  const handlePaymentRequested = (orderId: string, payment: Payment) => {
    // Ensure orderId is a string
    const orderIdStr = orderId.toString();
    setPayments((prev) => ({
      ...prev,
      [orderIdStr]: payment,
    }));
  };

  useEffect(() => {
    if (orderAnimate) {
      setTimeout(() => {
        setOrderAnimate("");
      }, 500);
    }
  }, [orderAnimate]);

  const updateOrderStatus = async (
    orderId: string,
    newStatus: string,
    oldStatus?: string
  ) => {
    try {
      const oldIndex = orderStatuses.findIndex(
        (status) => status.status === oldStatus
      );
      const newIndex = orderStatuses.findIndex(
        (status) => status.status === newStatus
      );

      // Set animation class first
      if (selectedStatus !== "all") {
        if (oldIndex > newIndex) {
          setOrderAnimate(`${orderId}/animate-slide-left`);
        } else {
          setOrderAnimate(`${orderId}/animate-slide-right`);
        }
      } else {
        setOrderAnimate(`${orderId}`);
      }

      // Make API call immediately (don't delay the actual update)
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/v1/admin/orders/${orderId}/status`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("auth-token")}`,
          },
          body: JSON.stringify({ status: newStatus }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update order status");
      }

      const result = await response.json();
      if (result.success) {
        // Delay the UI update to allow animation to complete
        setTimeout(() => {
          // Perform the optimistic update after animation
          if (useSocketOrders && updateOrderOptimistically) {
            updateOrderOptimistically(orderId, { status: newStatus });
          } else {
            setAllOrders((prevOrders) =>
              prevOrders.map((order) =>
                order._id === orderId
                  ? { ...order, status: newStatus as Order["status"] }
                  : order
              )
            );
          }

          // Clear animation state
          setOrderAnimate("");
          toast.success("Order status updated successfully");
        }, 100); // Match your animation duration
      } else {
        throw new Error(result.message || "Failed to update order status");
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error("Failed to update order status");

      // Clear animation state on error
      setOrderAnimate("");

      // Revert optimistic update on error
      if (socket) {
        socket.emit("refresh-orders");
      }
    }
  };

  const handleDeleteOrder = async (orderId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this order? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      const response = await deleteOrder(orderId);
      if (response.success) {
        toast.success("Order deleted successfully");
        // Remove the order from the local state
        // The socket will handle updating the orders list
        if (socket) {
          socket.emit("refresh-orders");
        }
      } else {
        toast.error(response.message || "Failed to delete order");
      }
    } catch (error) {
      console.error("Error deleting order:", error);
      toast.error("An error occurred while deleting the order");
    }
  };
  const handleMoreOptions = (order: Order) => {
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }
    setSelectedOrder(order);
    setShowOrderManagement(true);
  };

  const handleUpdateOrderPaymentStatus = async (
    orderId: string,
    paymentStatus: string,
    paymentMethod?: string
  ) => {
    try {
      const response = await updateOrderPaymentStatus(
        orderId,
        paymentStatus,
        paymentMethod
      );
      if (response.success) {
        // The socket will provide the authoritative update and show toast
      } else {
        toast.error("Failed to update payment status");
        // Refresh orders on error
        if (socket) {
          socket.emit("refresh-orders");
        }
      }
    } catch (error) {
      console.error("Error updating payment status:", error);
      toast.error("Failed to update payment status");
      // Refresh orders on error
      if (socket) {
        socket.emit("refresh-orders");
      }
    }
  };

  const handleNavigateToOrder = (orderId: string) => {
    // Find the order in the current list
    const order = orders.find((o) => o._id === orderId);

    if (order) {
      // Check if order is visible in current filtered view
      const isOrderVisible = filteredOrders.some((o) => o._id === orderId);

      if (isOrderVisible) {
        // Highlight the order temporarily
        setHighlightedOrderId(orderId);

        // Scroll to the order if it's visible
        const orderElement = document.getElementById(`order-${orderId}`);
        if (orderElement) {
          orderElement.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });

          // Remove highlight after 3 seconds
          setTimeout(() => {
            setHighlightedOrderId(null);
          }, 3000);
        }
      } else {
        // Order exists but is filtered out - switch to "All" view to show it
        toast.info(`Order #${order.orderNumber} updated`, {
          description: "Switching to 'All' view to show the updated order",
          action: {
            label: "View Order",
            onClick: () => {
              // Switch to "All" status and "All" outlets to ensure order is visible
              setSelectedStatus("all");
              setSelectedOutlet("all");

              // Update URL
              const url = new URL(window.location.href);
              url.searchParams.set("status", "all");
              router.push(url.pathname + url.search);

              // Highlight the order after a short delay to allow for state update
              setTimeout(() => {
                setHighlightedOrderId(orderId);
                const orderElement = document.getElementById(
                  `order-${orderId}`
                );
                if (orderElement) {
                  orderElement.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });
                }

                // Remove highlight after 3 seconds
                setTimeout(() => {
                  setHighlightedOrderId(null);
                }, 3000);
              }, 100);
            },
          },
        });
      }
    } else {
      // Order not found in current view, suggest refresh
      toast.info("Order updated", {
        description:
          "The updated order may not be visible in current view. Click to refresh.",
        action: {
          label: "Refresh",
          onClick: () => window.location.reload(),
        },
      });
    }
  };

  const handleViewFeedback = () => {
    setSelectedOrderForFeedback(true);
    setShowFeedbackDialog(true);
  };

  const handleMiscellaneousAmount = (order: Order) => {
    setSelectedOrderForMiscAmount(order);
    setShowMiscAmountDialog(true);
  };

  const ActionButton = ({
    icon: Icon,
    label,
    variant = "default",
    onClick,
    disabled,
    className = "",
  }: {
    icon: React.ElementType;
    label: string;
    variant?: "default" | "warning" | "danger" | "info";
    onClick: () => void;
    disabled?: boolean;
    className?: string;
  }) => {
    const variants = {
      info: "bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200 text-gray-700 hover:from-gray-100 hover:to-gray-200 hover:shadow-md hover:shadow-gray-200/50",
      default:
        "bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 text-blue-700 hover:from-blue-100 hover:to-blue-200 hover:shadow-md hover:shadow-blue-200/50",
      warning:
        "bg-gradient-to-r from-yellow-50 to-amber-100 border-yellow-200 text-yellow-700 hover:from-yellow-100 hover:to-amber-200 hover:shadow-md hover:shadow-yellow-200/50",
      danger:
        "bg-gradient-to-r from-red-50 to-red-100 border-red-200 text-red-700 hover:from-red-100 hover:to-red-200 hover:shadow-md hover:shadow-red-200/50",
    };

    return (
      <button
        onClick={onClick}
        disabled={disabled}
        className={`
          group relative px-3 py-2 rounded-xl border-2 
          transition-all duration-300 transform hover:scale-105 active:scale-95
          disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
          flex items-center gap-2 font-medium text-sm
          ${variants[variant]}
          ${className}
        `}
      >
        <Icon className="h-4 w-4 transition-transform group-hover:rotate-12" />
        <span className="hidden sm:inline">{label}</span>
        <div className="absolute inset-0 rounded-xl bg-white opacity-0 group-hover:opacity-10 transition-opacity" />
      </button>
    );
  };

  if (loading) {
    return <div>Loading orders...</div>;
  }

  return (
    <div className="p-1">
      <div className="md:mb-4 mb-2 flex flex-col gap-4">
        <div className="flex justify-between items-center flex-wrap">
          <select
            value={selectedOutlet}
            onChange={(e) => setSelectedOutlet(e.target.value)}
            className="border p-2 rounded-md"
          >
            <option value="all">All Outlets</option>
            {outlets.map((outlet) => (
              <option key={outlet._id} value={outlet._id}>
                {formatOutletDisplay(outlet)}
              </option>
            ))}
          </select>

          <div className="flex gap-2">
            {/* <Button
              onClick={() => {
                if (socket) {
                  socket.emit("refresh-orders");
                  toast.info("Refreshing orders...");
                }
              }}
              className="bg-primary text-white hover:bg-primary/90"
            >
              <RefreshCcw /> <div className="hidden md:block"> Refresh</div>
            </Button> */}
          </div>
        </div>
        <Tabs
          defaultValue={selectedStatus}
          className="w-full"
          onValueChange={(e) => {
            const url = new URL(window.location.href);
            url.searchParams.set("status", e);
            router.push(url.pathname + url.search);
            setSelectedStatus(e);
          }}
        >
          <TabsList className="grid grid-cols-6 w-full sticky top-16">
            <TabsTrigger className="text-xs md:text-md" value="all">
              All
            </TabsTrigger>
            <TabsTrigger className="text-xs md:text-md" value="pending">
              Pending
            </TabsTrigger>
            <TabsTrigger className="text-xs md:text-md" value="preparing">
              Preparing
            </TabsTrigger>
            <TabsTrigger className="text-xs md:text-md" value="ready">
              Ready
            </TabsTrigger>
            <TabsTrigger className="text-xs md:text-md" value="completed">
              Completed
            </TabsTrigger>
            {/* <TabsTrigger value="cancelled">Cancelled</TabsTrigger> */}
          </TabsList>
        </Tabs>
      </div>

      <div className="space-y-8">
        {filteredOrders
          .map((order, index) => {
            // Create a unique key that includes order data to force re-render on updates
            const orderKey = `${order._id}-${order.status}-${
              order.totalAmount
            }-${order.items.length}-${
              order.updatedAt || order.createdAt
            }-${index}`;
            const animateClass = orderAnimate.includes(order._id)
              ? orderAnimate.includes("animate-slide-left")
                ? "animate-slide-left animate-pulse duration-500"
                : orderAnimate.includes("animate-slide-right")
                ? "animate-slide-right animate-pulse duration-500"
                : "animate-pulse duration-500"
              : "bounce-once";
            const statusBorderClass =
              orderStatuses.find((s) => s.status === order.status)
                ?.borderClass ?? "border-gray-800";
            return (
              <div
                key={orderKey}
                id={`order-${order._id}`}
                className={`
    relative md:border-2 border p-1 md:p-3 rounded-lg bg-white
    shadow-sm hover:shadow-lg transition-all duration-300
    ${animateClass}
    ${
      highlightedOrderId === order._id
        ? "border-orange-400 bg-orange-50 shadow-lg ring-2 ring-orange-200"
        : statusBorderClass
    }
  `}
                style={{
                  animationDelay: `${
                    (animateClass === "bounce-once" ? index : 0) * 80
                  }ms`,
                }}
              >
                {["high", "urgent"].includes(order.priority) && (
                  <div
                    className={`absolute -top-4 p-1 -left-0 rounded-full w-32 text-center ${
                      prioritiesStyle[order.priority]
                    }`}
                  >
                    {order.priority.charAt(0).toUpperCase() +
                      order.priority.slice(1)}
                  </div>
                )}

                {/* Header Section */}
                <div className="w-full flex items-center justify-center p-2">
                  <div className="w-full max-w-4xl">
                    <div className="relative flex items-center">
                      {/* Progress Line Background */}
                      <div className="absolute left-0 right-0 h-0.5 bg-gray-200 top-1/4 -translate-y-1/4 mx-8 md:mx-12" />

                      {/* Active Progress Line */}
                      {(() => {
                        const currentOrder = getOrderStatusNumber(order.status);
                        const activeStatus = orderStatuses.find(
                          (s) => s.order === currentOrder
                        );

                        return (
                          <div
                            className={`
        absolute left-0 h-0.5 top-1/4 -translate-y-1/4
        mx-8 md:mx-12 transition-all duration-500 ease-out
        bg-gradient-to-r ${
          activeStatus?.progressColor ?? "from-gray-300 to-gray-400"
        }
      `}
                            style={{
                              width: `calc(${
                                ((currentOrder - 1) /
                                  (orderStatuses.length - 1)) *
                                100
                              }% - 1rem)`,
                            }}
                          />
                        );
                      })()}

                      {/* Status Steps */}
                      <div className="relative flex justify-between w-full">
                        {orderStatuses.map((status, i) => {
                          const currentOrder = getOrderStatusNumber(
                            order.status
                          );

                          // ✅ Correct logic
                          const isCompleted = currentOrder >= status.order;
                          const isActive = currentOrder === status.order;

                          return (
                            <div
                              key={i}
                              className="flex flex-col items-center flex-1 first:items-start last:items-end"
                            >
                              {/* Circle Button */}
                              <button
                                onClick={() => {
                                  if (order.status !== status.status)
                                    updateOrderStatus(
                                      order._id,
                                      status.status,
                                      order.status
                                    );
                                }}
                                className={`
            relative z-10 w-10 h-10 md:w-12 md:h-12 rounded-full border-2
            flex items-center justify-center transition-all duration-300

            ${
              isCompleted
                ? `${status.bgClass} ${status.borderClass} shadow-lg ${status.shadowClass} scale-110`
                : isActive
                ? `${status.bgClass}/20 ${status.borderClass} shadow-md`
                : "bg-white border-gray-300 hover:border-gray-400"
            }

            hover:scale-105
            focus:outline-none focus:ring-2 ${
              status.ringClass
            } focus:ring-offset-2
          `}
                              >
                                {isCompleted ? (
                                  <Check
                                    className="w-5 h-5 md:w-6 md:h-6 text-white"
                                    strokeWidth={3}
                                  />
                                ) : (
                                  <span
                                    className={`
                text-sm md:text-base font-semibold
                ${isActive ? status.textClass : "text-gray-400"}
              `}
                                  >
                                    {i + 1}
                                  </span>
                                )}
                              </button>

                              {/* Label */}
                              <div className="mt-3 text-center max-w-[100px]">
                                <p
                                  className={`
              text-xs md:text-sm font-medium transition-colors duration-300
              ${
                isCompleted
                  ? status.textClass
                  : isActive
                  ? "text-gray-900"
                  : "text-gray-400"
              }
            `}
                                >
                                  {status.status.charAt(0).toUpperCase() +
                                    status.status.slice(1)}
                                </p>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
                {/* order number */}

                <div className="p-2 relative overflow-hidden">
                  {/* Decorative elements */}
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full blur-3xl opacity-20" />
                  <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-pink-100 to-orange-100 rounded-full blur-3xl opacity-20" />

                  <div className="relative">
                    {/* Header Section */}
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                      {/* Order Info */}
                      <div className="flex items-start gap-4">
                        <div className="hidden sm:flex items-center justify-center w-14 h-14 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg shadow-indigo-500/30">
                          <Package
                            className="h-7 w-7 text-white"
                            strokeWidth={2.5}
                          />
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-3 flex-wrap">
                            <h3 className="text-xl md:text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                              #{order.orderNumber}
                            </h3>
                          </div>

                          <div className="flex items-center gap-2 mt-2 text-gray-600">
                            <Calendar className="h-4 w-4" />
                            <p className="text-sm md:text-base">
                              {new Date(order.createdAt).toLocaleDateString(
                                "en-US",
                                {
                                  month: "short",
                                  day: "numeric",
                                  year: "numeric",
                                  hour: "2-digit",
                                  minute: "2-digit",
                                }
                              )}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-3 flex-wrap">
                        {/* Bill Dropdown */}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="group rounded-xl border-2 bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 text-blue-700 hover:from-blue-100 hover:to-blue-200 hover:shadow-md hover:shadow-blue-200/50 transition-all duration-300 transform hover:scale-105 active:scale-95"
                            >
                              <FileDown className="h-4 w-4 mr-2 transition-transform group-hover:rotate-12" />
                              <span className="hidden sm:inline">Bill</span>
                              <ChevronDown className="h-3 w-3 ml-2" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent
                            align="end"
                            className="w-64 rounded-2xl border-gray-100"
                          >
                            <DropdownMenuLabel className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                              Invoice Actions
                            </DropdownMenuLabel>
                            <DropdownMenuSeparator className="bg-gradient-to-r from-transparent via-gray-200 to-transparent" />

                            <DropdownMenuItem
                              onClick={() => downloadOrderBill(order)}
                              className="cursor-pointer hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50"
                            >
                              <FileDown className="h-4 w-4 mr-2" />
                              Download PDF
                            </DropdownMenuItem>

                            {order.userId.email ? (
                              <DropdownMenuItem
                                onClick={() => handleEmailInvoice(order)}
                                disabled={emailingInvoices.has(order._id)}
                                className="cursor-pointer hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50"
                              >
                                <Mail className="h-4 w-4 mr-2" />
                                {emailingInvoices.has(order._id)
                                  ? "Sending..."
                                  : `Email to ${
                                      order.userId.email.split("@")[0]
                                    }...`}
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem disabled>
                                <Mail className="h-4 w-4 mr-2" />
                                Email not available
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>

                        {/* Update Items Button */}
                        {order.paymentStatus !== "paid" && (
                          <ActionButton
                            icon={Edit}
                            label="Update Items"
                            variant="default"
                            onClick={() => {
                              setOrderToUpdate(order);
                              setShowOrderUpdate(true);
                            }}
                          />
                        )}

                        {/* Miscellaneous Amount */}
                        <ActionButton
                          icon={Plus}
                          label="Misc Amount"
                          variant="warning"
                          onClick={() => handleMiscellaneousAmount(order)}
                        />

                        {/* Delete Button */}
                        <ActionButton
                          icon={Trash2}
                          label="Delete"
                          variant="danger"
                          onClick={() => handleDeleteOrder(order._id)}
                        />

                        {/* More Options */}
                        <ActionButton
                          icon={MoreHorizontal}
                          label="More"
                          variant="info"
                          onClick={() => handleMoreOptions(order)}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* <div className="w-full h-px bg-gray-200"></div> */}

                {/* Customer & Outlet Info */}
                <div className="grid grid-cols-2 gap-6 mb-2 md:mb-4">
                  <div className="space-y-2">
                    <h4 className="text-sm font-semibold text-gray-800">
                      Customer Details
                    </h4>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="flex items-center gap-2">
                        <UserIcon className="h-4 w-4 mr-2" />
                        <p className="text-gray-800 font-medium">
                          {order.userId.name}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 mr-2" />
                        <p className="text-gray-600">{order.userId.phone}</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-sm font-semibold text-gray-500">
                      Outlet
                    </h4>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      {(order?.tableInfo || order?.tableNumber) && (
                        <p className="font-bold text-sm">
                          Table:{" "}
                          {order?.tableInfo
                            ? `${order.tableInfo.name}${
                                order.tableInfo.location
                                  ? ` (${order.tableInfo.location})`
                                  : ""
                              }`
                            : order?.tableNumber}
                        </p>
                      )}
                      <p className="text-gray-800">{order?.outletId?.name}</p>
                      <p className="text-gray-600 text-sm">
                        {order?.outletId?.address}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Special Instructions */}
                {order.specialInstructions && (
                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-100 rounded-lg">
                    <h4 className="text-sm font-semibold text-yellow-800 mb-1">
                      Special Instructions
                    </h4>
                    <p className="text-yellow-700">
                      {order.specialInstructions}
                    </p>
                  </div>
                )}

                {/* Order Items */}
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-gray-500">
                    Order Items
                  </h4>
                  <div className="bg-gray-50 rounded-lg p-4">
                    {order.items.map((item, index) => {
                      const dishKey = `${order._id}-${index}`;
                      const isUpdating = updatingDishes.has(dishKey);

                      return (
                        <div
                          key={index}
                          className={`flex justify-between items-center py-3 ${
                            index !== order.items.length - 1
                              ? "border-b border-gray-200"
                              : ""
                          } ${
                            item.isServed
                              ? "bg-green-50 border-green-200 rounded-md mb-2"
                              : ""
                          }`}
                        >
                          <div className="flex items-center gap-3">
                            <Checkbox
                              checked={item.isServed || false}
                              onCheckedChange={() =>
                                handleDishServingToggle(
                                  order._id,
                                  index,
                                  item.isServed || false
                                )
                              }
                              disabled={isUpdating}
                              className="mt-1"
                            />
                            <span className="bg-primary/10 text-primary px-2 py-1 rounded-full text-sm">
                              {item.quantity}x
                            </span>
                            <div className="flex flex-col">
                              <span
                                className={`text-gray-800 ${
                                  item.isServed
                                    ? "line-through text-gray-500"
                                    : ""
                                }`}
                              >
                                {item.dishId?.name ||
                                  item.dishName ||
                                  "Deleted Dish"}
                              </span>
                              {!item.dishId && (
                                <span className="text-xs text-red-500 ml-2">
                                  (Dish no longer available)
                                </span>
                              )}
                              {((item.servedQuantity &&
                                item.servedQuantity > 0) ||
                                item.isServed) && (
                                <span className="text-xs text-green-600 flex items-center gap-1">
                                  <CheckCircle className="h-3 w-3" />
                                  {item.servedQuantity &&
                                  item.servedQuantity > 0 ? (
                                    <>
                                      Served: {item.servedQuantity}/
                                      {item.quantity}
                                      {item.servedQuantity < item.quantity && (
                                        <span className="text-orange-600">
                                          •{" "}
                                          {item.quantity - item.servedQuantity}{" "}
                                          pending
                                        </span>
                                      )}
                                    </>
                                  ) : (
                                    "Served"
                                  )}
                                  {item.servedAt && (
                                    <span className="text-gray-500">
                                      •{" "}
                                      {new Date(
                                        item.servedAt
                                      ).toLocaleTimeString()}
                                    </span>
                                  )}
                                </span>
                              )}
                            </div>

                            {/* Add-ons list under item name if present */}
                            {Array.isArray(item.addOns) &&
                              item.addOns.length > 0 && (
                                <div className="mt-1 ml-8">
                                  <div className="text-[11px] text-gray-500">
                                    Add-ons:
                                  </div>
                                  <ul className="ml-4 list-disc text-[11px] text-gray-600">
                                    {item.addOns.map((ao, i) => (
                                      <li key={i}>
                                        {ao.name} x{ao.quantity || 1} (₹
                                        {ao.price})
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                          </div>
                          <span className="text-gray-600">
                            ₹
                            {(() => {
                              const dishPrice =
                                item.dishId?.price || item.price || 0;
                              const addOnsTotal = Array.isArray(item.addOns)
                                ? item.addOns.reduce(
                                    (sum, ao) =>
                                      sum + ao.price * (ao.quantity || 1),
                                    0
                                  )
                                : 0;
                              return (
                                dishPrice * item.quantity +
                                addOnsTotal
                              ).toFixed(2);
                            })()}
                          </span>
                        </div>
                      );
                    })}
                    {/* Total Amount */}
                    <div className="mt-4 pt-3 border-t border-gray-300 space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="font-semibold text-gray-700">
                          Subtotal
                        </span>
                        <span className="font-semibold text-gray-700">
                          ₹{order.totalAmount.toFixed(2)}
                        </span>
                      </div>
                      {order.appliedOffers &&
                        order.appliedOffers.map((offer, i) => (
                          <div
                            className="flex justify-between items-center"
                            key={i}
                          >
                            <span className="font-semibold text-green-500">
                              {offer.offerName}
                            </span>
                            <span className="font-semibold text-green-500">
                              - ₹{offer.discount}
                            </span>
                          </div>
                        ))}

                      {order.couponCode &&
                        order.couponDiscount &&
                        order.couponDiscount > 0 && (
                          <div className="flex justify-between items-center text-green-600">
                            <span className="font-semibold">
                              Coupon Discount ({order.couponCode})
                            </span>
                            <span className="font-semibold">
                              -₹{order.couponDiscount.toFixed(2)}
                            </span>
                          </div>
                        )}

                      <div className="flex justify-between items-center">
                        <span className="font-semibold text-gray-700">
                          Final Amount
                        </span>
                        <span className="text-lg font-bold text-primary">
                          ₹{(order.finalAmount || order.totalAmount).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Section */}
                <div className="md:mt-6 border-gray-200 flex justify-between items-center flex-wrap">
                  <div>
                    <h4 className="text-sm font-semibold text-gray-500 mb-3">
                      Payment Information
                    </h4>

                    <div className="flex items-center gap-2 mb-3 flex-wrap">
                      <span className="text-sm text-gray-600">
                        Payment Status:
                      </span>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          order.paymentStatus === "paid"
                            ? "bg-green-100 text-green-800"
                            : order.paymentStatus === "requested"
                            ? "bg-blue-100 text-blue-800"
                            : order.paymentStatus === "failed"
                            ? "bg-red-100 text-red-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {order.paymentStatus?.toUpperCase() || "PENDING"}
                      </span>
                      {order.paymentStatus === "pending" ? (
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100 "
                          onClick={() =>
                            handleUpdateOrderPaymentStatus(
                              order._id,
                              "paid",
                              "cash"
                            )
                          }
                        >
                          <Check className="h-3 w-3 mr-1" />{" "}
                          <span className="hidden md:block">Mark as</span> Paid
                        </Button>
                      ) : (
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                          onClick={() =>
                            handleUpdateOrderPaymentStatus(order._id, "pending")
                          }
                        >
                          <X className="h-3 w-3 mr-1" />{" "}
                          <span className="hidden md:block">Mark as</span>
                          Pending
                        </Button>
                      )}
                      <span className="text-sm text-gray-600 md:ml-4">
                        Method:
                      </span>
                      <span className="text-sm font-medium">
                        {order.paymentMethod === "online"
                          ? "Online Payment"
                          : "Cash"}
                      </span>
                    </div>
                  </div>

                  {/* Payment Actions */}
                  <div className="grid grid-cols-1 gap-4">
                    {/* Show payment request button if payment is not yet requested or failed */}
                    {(!payments[order._id] ||
                      order.paymentStatus === "pending" ||
                      order.paymentStatus === "failed") && (
                      <PaymentRequestButton
                        orderId={order._id.toString()}
                        paymentStatus={order.paymentStatus || "pending"}
                        onPaymentRequested={(payment) =>
                          handlePaymentRequested(order._id, payment)
                        }
                      />
                    )}

                    {/* Show payment link if payment has been requested */}
                  </div>
                </div>
                {payments[order._id] && (
                  <PaymentLinkDisplay
                    payment={payments[order._id]}
                    isAdmin={true}
                  />
                )}
              </div>
            );
          })
          // .sort((a, b) => {
          //   const aDate = new Date(a.updatedAt);
          //   const bDate = new Date(b.updatedAt);
          //   return bDate.getTime() - aDate.getTime();
          // })
          }
      </div>

      {/* Load More Button */}
      {!useSocketOrders && pagination.hasNext && (
        <div className="flex justify-center mt-6">
          <Button
            variant="outline"
            onClick={loadMoreOrders}
            disabled={loadingMore}
            className="min-w-32"
          >
            {loadingMore ? (
              <>
                <Clock className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Load More Orders"
            )}
          </Button>
        </div>
      )}

      {/* Order Management Dialog */}
      {selectedOrder && (
        <OrderManagementDialog
          order={selectedOrder}
          staffList={staff}
          open={showOrderManagement}
          // onOpenChange={setShowOrderManagement}
          onOpenChange={(e) => {
            console.log(e, "the passed value 1446");
            setShowOrderManagement(e);
            setSelectedOrder(null);
            // window.location.reload();
          }}
          onSuccess={() => {
            // Refresh the orders
            setShowOrderManagement(false);
            setSelectedOrder(null);
          }}
        />
      )}

      {/* Create Order Dialog */}
      <CreateOrderDialog
        open={showCreateOrder}
        onOpenChange={setShowCreateOrder}
        onSuccess={() => {
          // Refresh the orders
          setShowCreateOrder(false);
          window.location.reload();
        }}
        outletId={selectedOutlet}
      />

      {/* Admin Order Update Dialog */}
      {orderToUpdate && (
        <AdminOrderUpdateDialog
          order={orderToUpdate}
          isOpen={showOrderUpdate}
          onClose={() => {
            setShowOrderUpdate(false);
            setOrderToUpdate(null);
          }}
          onSuccess={() => {
            // Refresh the orders
            setShowOrderUpdate(false);
            setOrderToUpdate(null);
            // window.location.reload();
          }}
        />
      )}

      <OrderSearch orders={orders} />
      <div className="flex items-center gap-2 fixed right-0 bottom-36 shadow-lg p-2 rounded-l-md bg-whiet border-2 border-gray-800 border-r-0">
        <Button
          onClick={() => setShowCreateOrder(true)}
          className="bg-primary text-white hover:bg-primary/90 rounded-r-none"
        >
          <PlusCircle className="animate-pulse" />
          <div className="hidden md:block"> Create Order</div>
        </Button>
      </div>

      {/* Order Update Notifications */}
      <OrderUpdateNotifications
        onNavigateToOrder={handleNavigateToOrder}
        onRefreshOrders={refreshOrders}
      />

      {/* Feedback Dialog */}
      {selectedOrderForFeedback && (
        <FeedbackViewDialog
          isOpen={showFeedbackDialog}
          onClose={() => {
            setShowFeedbackDialog(false);
            setSelectedOrderForFeedback(false);
          }}
          outletId={selectedOutlet}
          mode="order"
        />
      )}
      <div className="flex items-center gap-2 absolute right-0 top-20 shadow-lg p-2 rounded-l-md bg-white border-2 border-gray-800 border-r-0">
        <div onClick={() => handleViewFeedback()} className="cursor-pointer">
          <MessageSquare />
        </div>
      </div>

      {/* Miscellaneous Amount Dialog */}
      {selectedOrderForMiscAmount && (
        <MiscellaneousAmountDialog
          isOpen={showMiscAmountDialog}
          onClose={() => {
            setShowMiscAmountDialog(false);
            setSelectedOrderForMiscAmount(null);
          }}
          orderId={selectedOrderForMiscAmount._id}
          currentAmount={selectedOrderForMiscAmount.miscellaneousAmount || 0}
          currentReason={selectedOrderForMiscAmount.miscellaneousReason || ""}
          onUpdate={() => {
            // Refresh orders after update
            refreshOrders();
          }}
        />
      )}
    </div>
  );
};

const Page = () => {
  return (
    <Suspense>
      <AdminOrdersPage />
    </Suspense>
  );
};

export default Page;
