/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import Image from "next/image";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  MapPin,
  Phone,
  Mail,
  Globe,
  MessageCircle,
  Star,
  ChefHat,
  Store,
  Users,
  Search,
  X,
  Share2,
  TrendingUp,
  Award,
  Heart,
  Facebook,
  Instagram,
  Twitter,
  Youtube,
  Linkedin,
  Info,
  Utensils,
  Wifi,
  Car,
  Music,
  Baby,
  Percent,
  CheckCircle,
  Sparkles,
} from "lucide-react";
import { toast } from "sonner";
import { stringReducer } from "../helper/general";
import { Input } from "@/components/ui/input";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { firstLetterExtractor } from "../helper/chat";
import { getOutletMenu } from "@/server/user";

// Extended interfaces with new fields
interface Theme {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  logoUrl: string;
  favIcon: string;
  bannerImage?: string;
  fontFamily?: string;
}

interface PageConfig {
  enableHeroSection?: boolean;
  enableAboutSection?: boolean;
  enableOutletsSection?: boolean;
  enableMenuSection?: boolean;
  enableTestimonialsSection?: boolean;
  enableGallerySection?: boolean;
  enableFeaturedDishes?: boolean;
  enableNewsSection?: boolean;
  enableFAQSection?: boolean;
  enableContactForm?: boolean;
  enableSocialLinks?: boolean;
  showStats?: boolean;
  showRatings?: boolean;
  showPricing?: boolean;
  heroStyle?: "gradient" | "image" | "video" | "minimal";
  menuLayout?: "grid" | "list" | "masonry";
}

interface HeroSection {
  enabled?: boolean;
  title?: string;
  subtitle?: string;
  backgroundImage?: string;
  backgroundVideo?: string;
  ctaText?: string;
  ctaLink?: string;
  overlayOpacity?: number;
}

interface AboutSection {
  enabled?: boolean;
  title?: string;
  content?: string;
  images?: string[];
  highlights?: Array<{
    icon?: string;
    title?: string;
    description?: string;
  }>;
}

interface Gallery {
  enabled?: boolean;
  title?: string;
  images?: Array<{
    url: string;
    caption?: string;
    category?: string;
  }>;
}

interface Testimonial {
  customerName: string;
  customerImage?: string;
  rating: number;
  review: string;
  date: Date;
  featured?: boolean;
}

interface Announcement {
  title: string;
  content: string;
  type: "promotion" | "event" | "news" | "alert";
  image?: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  priority: number;
}

interface Promotion {
  title: string;
  description: string;
  code?: string;
  discountType: "percentage" | "fixed";
  discountValue: number;
  image?: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
}

interface FAQ {
  question: string;
  answer: string;
  category?: string;
  order: number;
}

interface SocialMedia {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  youtube?: string;
  linkedin?: string;
  whatsapp?: string;
}

interface Features {
  onlineOrdering?: boolean;
  tableReservation?: boolean;
  homeDelivery?: boolean;
  takeaway?: boolean;
  dineIn?: boolean;
  catering?: boolean;
  vegetarianOptions?: boolean;
  veganOptions?: boolean;
  freeWifi?: boolean;
  parking?: boolean;
  outdoorSeating?: boolean;
  liveMusic?: boolean;
  kidsPlay?: boolean;
}

interface Outlet {
  _id: string;
  name: string;
  address: string;
  contact: string;
}

interface Category {
  _id: string;
  name: string;
  description: string;
}

interface Dish {
  _id: string;
  name: string;
  description: string;
  price: number;
  image?: string;
  category: Category;
  isVeg: boolean;
  isAvailable: boolean;
  isFeatured: boolean;
  rating?: number;
}

interface FoodChain {
  _id: string;
  name: string;
  tagline?: string;
  description?: string;
  contact: string;
  email?: string;
  website?: string;
  theme: Theme;
  pageConfig?: PageConfig;
  heroSection?: HeroSection;
  aboutSection?: AboutSection;
  gallery?: Gallery;
  testimonials?: {
    enabled?: boolean;
    title?: string;
    reviews?: Testimonial[];
  };
  announcements?: Announcement[];
  promotions?: Promotion[];
  faqs?: FAQ[];
  socialMedia?: SocialMedia;
  features?: Features;
  outlets: Outlet[];
  categories: Category[];
  dishes: Dish[];
  stats: {
    totalOrders: number;
    totalRevenue: number;
    totalCustomers: number;
    avgOrderValue: number;
  };
  createdAt: string;
  updatedAt: string;
  customization?: any;
  loyaltyProgram?: any;
  operatingHours?: any;
  analytics?: any;
}

const FullFeaturedFoodChainPage = () => {
  const params = useParams();
  const router = useRouter();
  const [foodChain, setFoodChain] = useState<FoodChain | null>(null);
  const [loading, setLoading] = useState(true);
  const [search, setSearch] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [dishes, setDishes] = useState([]);
  const [selectedGalleryCategory, setSelectedGalleryCategory] =
    useState<string>("all");

  const foodchainId = params?.foodchainId as string;

  const fetchFoodChainData = async () => {
    try {
      setLoading(true);
      const backendUrl =
        process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3001";
      const response = await fetch(
        `${backendUrl}/api/v1/public/foodchain/${foodchainId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      if (!response.ok) {
        throw new Error("Food chain not found");
      }

      const data = await response.json();
      setFoodChain(data.data);
      if (data?.data?.outlets) {
        fetchDishes(data.data.outlets[0]._id);
      }
    } catch (error) {
      console.error("Error fetching food chain:", error);
      toast.error("Food chain not found");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (foodchainId) {
      fetchFoodChainData();
    }
  }, [foodchainId]);

  const fetchDishes = async (outletId:string) => {
    const res = await getOutletMenu(foodchainId, outletId);
    setDishes(res.data);
  };
  const handleChatWithOutlet = (outletId: string) => {
    router.push(`/chat?chainId=${foodchainId}&outletId=${outletId}`);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: foodChain?.name,
        text: `Check out ${foodChain?.name} on Butler`,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success("Link copied to clipboard!");
    }
  };

  const toggleFavorite = (dishId: string) => {
    setFavorites((prev) => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(dishId)) {
        newFavorites.delete(dishId);
        toast.success("Removed from favorites");
      } else {
        newFavorites.add(dishId);
        toast.success("Added to favorites");
      }
      return newFavorites;
    });
  };

  const filteredDishes =
    dishes
      ?.filter(
        (dish) =>
          selectedCategory === "all" || dish.category._id === selectedCategory,
      )
      ?.filter((dish) =>
        search
          ? dish.name.toLowerCase().includes(search.toLowerCase()) ||
            dish.description.toLowerCase().includes(search.toLowerCase())
          : true,
      ) || [];

  const featuredDishes = dishes?.filter((dish) => dish.isFeatured) || [];

  const activeAnnouncements =
    foodChain?.announcements
      ?.filter((ann) => {
        const now = new Date();
        return (
          ann.isActive &&
          new Date(ann.startDate) <= now &&
          new Date(ann.endDate) >= now
        );
      })
      .sort((a, b) => b.priority - a.priority) || [];

  const activePromotions =
    foodChain?.promotions?.filter((promo) => {
      const now = new Date();
      return (
        promo.isActive &&
        new Date(promo.startDate) <= now &&
        new Date(promo.endDate) >= now
      );
    }) || [];

  const galleryCategories = [
    "all",
    ...new Set(
      foodChain?.gallery?.images?.map((img) => img.category).filter(Boolean) ||
        [],
    ),
  ];

  const filteredGalleryImages =
    selectedGalleryCategory === "all"
      ? foodChain?.gallery?.images || []
      : foodChain?.gallery?.images?.filter(
          (img) => img.category === selectedGalleryCategory,
        ) || [];

  const getSocialIcon = (platform: string) => {
    const icons: { [key: string]: any } = {
      facebook: Facebook,
      instagram: Instagram,
      twitter: Twitter,
      youtube: Youtube,
      linkedin: Linkedin,
    };
    return icons[platform] || Globe;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Skeleton className="h-80 w-full rounded-3xl mb-8" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Skeleton className="h-32 rounded-2xl" />
            <Skeleton className="h-32 rounded-2xl" />
            <Skeleton className="h-32 rounded-2xl" />
          </div>
          <Skeleton className="h-96 w-full rounded-3xl" />
        </div>
      </div>
    );
  }

  if (!foodChain) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
        <Card className="max-w-md w-full text-center shadow-2xl border-0">
          <CardContent className="p-12">
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Store className="w-10 h-10 text-red-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-3">Not Found</h1>
            <p className="text-gray-600 mb-8">
              The food chain you&apos;re looking for doesn&apos;t exist.
            </p>
            <Button
              onClick={() => router.push("/")}
              size="lg"
              className="w-full"
            >
              Go Home
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const config = foodChain.pageConfig || {};
  const hero = foodChain.heroSection || {};

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100"
      style={{ fontFamily: foodChain?.theme?.fontFamily || "inherit" }}
    >
      {/* Fixed Share Button */}
      {config.enableSocialLinks !== false && (
        <button
          onClick={handleShare}
          className="fixed top-6 right-6 z-50 bg-white hover:bg-gray-50 shadow-lg hover:shadow-xl rounded-full h-10 w-10 flex items-center justify-center transition-all duration-300 transform hover:scale-110 group"
          aria-label="Share"
        >
          <Share2 className="h-4 w-4 text-gray-700 group-hover:text-gray-900 transition-colors" />
        </button>
      )}

      {/* Announcements Banner */}
      {config.enableNewsSection !== false && activeAnnouncements.length > 0 && (
        <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-white py-3 px-4 text-center">
          <div className="max-w-7xl mx-auto flex items-center justify-center gap-2">
            <Sparkles className="w-5 h-5 animate-pulse" />
            <p className="font-semibold">
              {activeAnnouncements[0].title}: {activeAnnouncements[0].content}
            </p>
          </div>
        </div>
      )}

      {/* Hero Section */}
      {config.enableHeroSection !== false && (
        <div className="relative overflow-hidden">
          <div
            className={`relative ${
              config.heroStyle === "minimal" ? "h-64" : "h-96"
            } bg-gradient-to-br`}
            style={{
              background:
                config.heroStyle === "image" && hero.backgroundImage
                  ? `url(${hero.backgroundImage})`
                  : `linear-gradient(135deg, ${foodChain.theme.primaryColor}, ${foodChain.theme.secondaryColor})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
          >
            {config.heroStyle === "video" && hero.backgroundVideo && (
              <video
                autoPlay
                loop
                muted
                playsInline
                className="absolute inset-0 w-full h-full object-cover"
              >
                <source src={hero.backgroundVideo} type="video/mp4" />
              </video>
            )}

            <div
              className="absolute inset-0 bg-black"
              style={{ opacity: hero.overlayOpacity || 0.3 }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />

            {/* Animated Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full filter blur-3xl animate-pulse" />
              <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full filter blur-3xl animate-pulse delay-1000" />
            </div>

            <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center">
              <div className="w-full">
                <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
                  {foodChain.theme.logoUrl && (
                    <div className="relative group">
                      <div className="absolute inset-0 bg-white rounded-2xl opacity-20 blur-xl group-hover:opacity-30 transition-opacity" />
                      <Avatar className="relative rounded-2xl size-28 border-4 border-white/30 shadow-2xl backdrop-blur-sm">
                        <AvatarImage
                          src={foodChain.theme.logoUrl}
                          alt={firstLetterExtractor(foodChain.name)}
                          className="object-cover"
                        />
                        <AvatarFallback className="text-3xl font-bold bg-white/90">
                          {stringReducer(
                            firstLetterExtractor(foodChain.name),
                            3,
                          )}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  )}

                  <div className="flex-1 text-center md:text-left">
                    <h1 className="text-5xl md:text-6xl font-bold mb-3 text-white drop-shadow-lg animate-fade-in">
                      {hero.title || foodChain.name}
                    </h1>
                    <p className="text-xl md:text-2xl text-white/95 mb-6 drop-shadow-md">
                      {hero.subtitle || foodChain.tagline}
                    </p>

                    {config.showStats !== false && (
                      <div className="flex flex-wrap items-center justify-center md:justify-start gap-6 mb-6">
                        <div className="flex items-center gap-2 bg-white/20 backdrop-blur-md px-4 py-2 rounded-full border border-white/30">
                          <Store className="w-5 h-5 text-white" />
                          <span className="text-white font-semibold">
                            {foodChain.outlets.length} Outlets
                          </span>
                        </div>
                        <div className="flex items-center gap-2 bg-white/20 backdrop-blur-md px-4 py-2 rounded-full border border-white/30">
                          <ChefHat className="w-5 h-5 text-white" />
                          <span className="text-white font-semibold">
                            {dishes?.length || 0} Dishes
                          </span>
                        </div>
                        <div className="flex items-center gap-2 bg-white/20 backdrop-blur-md px-4 py-2 rounded-full border border-white/30">
                          <Users className="w-5 h-5 text-white" />
                          <span className="text-white font-semibold">
                            {foodChain.stats.totalCustomers.toLocaleString()}{" "}
                            Customers
                          </span>
                        </div>
                      </div>
                    )}

                    {hero.ctaText && (
                      <Button
                        size="lg"
                        className="bg-white text-gray-900 hover:bg-gray-100 font-bold shadow-xl"
                        onClick={() => {
                          if (hero.ctaLink) {
                            window.location.href = hero.ctaLink;
                          } else if (foodChain.outlets.length > 0) {
                            handleChatWithOutlet(foodChain.outlets[0]._id);
                          }
                        }}
                      >
                        {hero.ctaText}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Wave Separator */}
          <div className="relative -mt-1">
            <svg
              viewBox="0 0 1440 120"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="w-full"
            >
              <path
                d="M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0V120Z"
                fill="white"
              />
            </svg>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-8 pb-16">
        {/* Active Promotions */}
        {activePromotions.length > 0 && (
          <div className="mb-12">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {activePromotions.map((promo, index) => (
                <Card
                  key={index}
                  className="border-0 shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                >
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 h-2" />
                  {promo.image && (
                    <div className="relative h-40">
                      <Image
                        src={promo.image}
                        alt={promo.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="font-bold text-xl text-gray-900">
                        {promo.title}
                      </h3>
                      <Badge className="bg-gradient-to-r from-purple-500 to-pink-500">
                        <Percent className="w-3 h-3 mr-1" />
                        {promo.discountType === "percentage"
                          ? `${promo.discountValue}%`
                          : `₹${promo.discountValue}`}
                      </Badge>
                    </div>
                    <p className="text-gray-600 text-sm mb-4">
                      {promo.description}
                    </p>
                    {promo.code && (
                      <div className="bg-gray-100 px-4 py-2 rounded-lg border-2 border-dashed border-gray-300 text-center">
                        <p className="text-xs text-gray-500 mb-1">Promo Code</p>
                        <p className="font-bold text-lg tracking-wider">
                          {promo.code}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Quick Stats Cards */}
        {config.showStats !== false && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-blue-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Total Orders
                    </p>
                    <p className="text-3xl font-bold text-gray-900">
                      {foodChain.stats.totalOrders.toLocaleString()}
                    </p>
                  </div>
                  <div className="bg-blue-100 p-4 rounded-2xl">
                    <TrendingUp className="w-8 h-8 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-green-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Avg Order Value
                    </p>
                    <p className="text-3xl font-bold text-gray-900">
                      ₹{foodChain.stats.avgOrderValue.toFixed(0)}
                    </p>
                  </div>
                  <div className="bg-green-100 p-4 rounded-2xl">
                    <Award className="w-8 h-8 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-purple-50 to-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">
                      Happy Customers
                    </p>
                    <p className="text-3xl font-bold text-gray-900">
                      {foodChain.stats.totalCustomers.toLocaleString()}
                    </p>
                  </div>
                  <div className="bg-purple-100 p-4 rounded-2xl">
                    <Users className="w-8 h-8 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* About Section */}
        {config.enableAboutSection !== false &&
          foodChain.aboutSection?.enabled !== false && (
            <Card className="mb-12 border-0 shadow-lg overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-white border-b">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <div
                    className="p-2 rounded-xl"
                    style={{
                      backgroundColor: `${foodChain.theme.primaryColor}20`,
                    }}
                  >
                    <Info
                      className="w-6 h-6"
                      style={{ color: foodChain.theme.primaryColor }}
                    />
                  </div>
                  <span>{foodChain.aboutSection?.title || "About Us"}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div>
                    <p className="text-gray-700 leading-relaxed mb-6">
                      {foodChain.aboutSection?.content || foodChain.description}
                    </p>

                    {/* Highlights */}
                    {foodChain.aboutSection?.highlights &&
                      foodChain.aboutSection.highlights.length > 0 && (
                        <div className="space-y-4">
                          {foodChain.aboutSection.highlights.map(
                            (highlight, index) => (
                              <div
                                key={index}
                                className="flex items-start gap-4 p-4 bg-gray-50 rounded-xl"
                              >
                                <div
                                  className="p-3 rounded-lg"
                                  style={{
                                    backgroundColor: `${foodChain.theme.primaryColor}20`,
                                  }}
                                >
                                  <CheckCircle
                                    className="w-6 h-6"
                                    style={{
                                      color: foodChain.theme.primaryColor,
                                    }}
                                  />
                                </div>
                                <div>
                                  <h4 className="font-semibold text-gray-900 mb-1">
                                    {highlight.title}
                                  </h4>
                                  <p className="text-sm text-gray-600">
                                    {highlight.description}
                                  </p>
                                </div>
                              </div>
                            ),
                          )}
                        </div>
                      )}
                  </div>

                  {/* Images */}
                  {foodChain.aboutSection?.images &&
                    foodChain.aboutSection.images.length > 0 && (
                      <div className="grid grid-cols-2 gap-4">
                        {foodChain.aboutSection.images
                          .slice(0, 4)
                          .map((img, index) => (
                            <div
                              key={index}
                              className="relative h-48 rounded-xl overflow-hidden"
                            >
                              <Image
                                src={img}
                                alt={`About ${index + 1}`}
                                fill
                                className="object-cover"
                              />
                            </div>
                          ))}
                      </div>
                    )}
                </div>
              </CardContent>
            </Card>
          )}

        {/* Features & Services */}
        {foodChain.features &&
          Object.values(foodChain.features).some((v) => v === true) && (
            <Card className="mb-12 border-0 shadow-lg overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-white border-b">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <div
                    className="p-2 rounded-xl"
                    style={{
                      backgroundColor: `${foodChain.theme.primaryColor}20`,
                    }}
                  >
                    <Sparkles
                      className="w-6 h-6"
                      style={{ color: foodChain.theme.primaryColor }}
                    />
                  </div>
                  <span>Our Features & Services</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                  {foodChain.features.onlineOrdering && (
                    <div className="text-center">
                      <div className="bg-blue-100 p-4 rounded-2xl w-16 h-16 flex items-center justify-center mx-auto mb-3">
                        <Globe className="w-8 h-8 text-blue-600" />
                      </div>
                      <p className="text-sm font-semibold text-gray-700">
                        Online Ordering
                      </p>
                    </div>
                  )}
                  {foodChain.features.homeDelivery && (
                    <div className="text-center">
                      <div className="bg-green-100 p-4 rounded-2xl w-16 h-16 flex items-center justify-center mx-auto mb-3">
                        <Car className="w-8 h-8 text-green-600" />
                      </div>
                      <p className="text-sm font-semibold text-gray-700">
                        Home Delivery
                      </p>
                    </div>
                  )}
                  {foodChain.features.dineIn && (
                    <div className="text-center">
                      <div className="bg-purple-100 p-4 rounded-2xl w-16 h-16 flex items-center justify-center mx-auto mb-3">
                        <Utensils className="w-8 h-8 text-purple-600" />
                      </div>
                      <p className="text-sm font-semibold text-gray-700">
                        Dine-In
                      </p>
                    </div>
                  )}
                  {foodChain.features.freeWifi && (
                    <div className="text-center">
                      <div className="bg-yellow-100 p-4 rounded-2xl w-16 h-16 flex items-center justify-center mx-auto mb-3">
                        <Wifi className="w-8 h-8 text-yellow-600" />
                      </div>
                      <p className="text-sm font-semibold text-gray-700">
                        Free WiFi
                      </p>
                    </div>
                  )}
                  {foodChain.features.parking && (
                    <div className="text-center">
                      <div className="bg-red-100 p-4 rounded-2xl w-16 h-16 flex items-center justify-center mx-auto mb-3">
                        <Car className="w-8 h-8 text-red-600" />
                      </div>
                      <p className="text-sm font-semibold text-gray-700">
                        Parking
                      </p>
                    </div>
                  )}
                  {foodChain.features.liveMusic && (
                    <div className="text-center">
                      <div className="bg-pink-100 p-4 rounded-2xl w-16 h-16 flex items-center justify-center mx-auto mb-3">
                        <Music className="w-8 h-8 text-pink-600" />
                      </div>
                      <p className="text-sm font-semibold text-gray-700">
                        Live Music
                      </p>
                    </div>
                  )}
                  {foodChain.features.kidsPlay && (
                    <div className="text-center">
                      <div className="bg-indigo-100 p-4 rounded-2xl w-16 h-16 flex items-center justify-center mx-auto mb-3">
                        <Baby className="w-8 h-8 text-indigo-600" />
                      </div>
                      <p className="text-sm font-semibold text-gray-700">
                        Kids Play Area
                      </p>
                    </div>
                  )}
                  {foodChain.features.vegetarianOptions && (
                    <div className="text-center">
                      <div className="bg-green-100 p-4 rounded-2xl w-16 h-16 flex items-center justify-center mx-auto mb-3">
                        <span className="text-2xl">🌱</span>
                      </div>
                      <p className="text-sm font-semibold text-gray-700">
                        Vegetarian
                      </p>
                    </div>
                  )}
                  {foodChain.features.veganOptions && (
                    <div className="text-center">
                      <div className="bg-lime-100 p-4 rounded-2xl w-16 h-16 flex items-center justify-center mx-auto mb-3">
                        <span className="text-2xl">🥗</span>
                      </div>
                      <p className="text-sm font-semibold text-gray-700">
                        Vegan Options
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

        {/* Contact Information */}
        <Card className="mb-12 border-0 shadow-lg overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-gray-50 to-white border-b">
            <CardTitle className="flex items-center gap-3 text-2xl">
              <div
                className="p-2 rounded-xl"
                style={{ backgroundColor: `${foodChain.theme.primaryColor}20` }}
              >
                <Phone
                  className="w-6 h-6"
                  style={{ color: foodChain.theme.primaryColor }}
                />
              </div>
              <span>Get in Touch</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <a
                href={`tel:${foodChain.contact}`}
                className="flex items-center gap-4 p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors group"
              >
                <div className="bg-white p-3 rounded-lg shadow-sm group-hover:shadow-md transition-shadow">
                  <Phone className="w-5 h-5 text-gray-700" />
                </div>
                <div>
                  <p className="text-xs text-gray-500 mb-1">Phone</p>
                  <p className="font-semibold text-gray-900">
                    {foodChain.contact}
                  </p>
                </div>
              </a>

              {foodChain.email && (
                <a
                  href={`mailto:${foodChain.email}`}
                  className="flex items-center gap-4 p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors group"
                >
                  <div className="bg-white p-3 rounded-lg shadow-sm group-hover:shadow-md transition-shadow">
                    <Mail className="w-5 h-5 text-gray-700" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-500 mb-1">Email</p>
                    <p className="font-semibold text-gray-900">
                      {foodChain.email}
                    </p>
                  </div>
                </a>
              )}

              {foodChain.website && (
                <a
                  href={foodChain.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-4 p-4 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors group"
                >
                  <div className="bg-white p-3 rounded-lg shadow-sm group-hover:shadow-md transition-shadow">
                    <Globe className="w-5 h-5 text-gray-700" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs text-gray-500 mb-1">Website</p>
                    <p className="font-semibold text-gray-900 truncate">
                      Visit Website
                    </p>
                  </div>
                </a>
              )}
            </div>

            {/* Social Media Links */}
            {config.enableSocialLinks !== false && foodChain.socialMedia && (
              <div className="border-t pt-6">
                <p className="text-sm font-semibold text-gray-700 mb-4">
                  Follow Us
                </p>
                <div className="flex flex-wrap gap-3">
                  {Object.entries(foodChain.socialMedia).map(
                    ([platform, url]) => {
                      if (!url) return null;
                      const Icon = getSocialIcon(platform);
                      return (
                        <a
                          key={platform}
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-3 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                          aria-label={platform}
                        >
                          <Icon className="w-5 h-5 text-gray-700" />
                        </a>
                      );
                    },
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Outlets Section */}
        {config.enableOutletsSection !== false && (
          <Card className="mb-12 border-0 shadow-lg overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-white border-b">
              <CardTitle className="flex items-center gap-3 text-2xl">
                <div
                  className="p-2 rounded-xl"
                  style={{
                    backgroundColor: `${foodChain.theme.primaryColor}20`,
                  }}
                >
                  <MapPin
                    className="w-6 h-6"
                    style={{ color: foodChain.theme.primaryColor }}
                  />
                </div>
                <span>Our Outlets</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {foodChain.outlets.map((outlet) => (
                  <Card
                    key={outlet._id}
                    className="border-2 border-gray-100 hover:border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 overflow-hidden group"
                  >
                    <div
                      className="h-2"
                      style={{
                        background: `linear-gradient(90deg, ${foodChain.theme.primaryColor}, ${foodChain.theme.secondaryColor})`,
                      }}
                    />
                    <CardContent className="p-6">
                      <h3 className="font-bold text-xl mb-4 text-gray-900 group-hover:text-gray-700 transition-colors">
                        {outlet.name}
                      </h3>
                      <div className="space-y-3 mb-6">
                        <div className="flex items-start gap-3 text-gray-600">
                          <MapPin className="w-5 h-5 mt-0.5 flex-shrink-0 text-gray-400" />
                          <span className="text-sm leading-relaxed">
                            {outlet.address}
                          </span>
                        </div>
                        <div className="flex items-center gap-3 text-gray-600">
                          <Phone className="w-5 h-5 flex-shrink-0 text-gray-400" />
                          <span className="text-sm font-medium">
                            {outlet.contact}
                          </span>
                        </div>
                      </div>
                      <Button
                        onClick={() => handleChatWithOutlet(outlet._id)}
                        className="w-full shadow-md hover:shadow-lg transition-all duration-300 group-hover:scale-105"
                        style={{
                          backgroundColor: foodChain.theme.primaryColor,
                        }}
                      >
                        <MessageCircle className="w-4 h-4 mr-2" />
                        Start Chat
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Featured Dishes */}
        {config.enableFeaturedDishes !== false && featuredDishes.length > 0 && (
          <Card className="mb-12 border-0 shadow-lg overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-white border-b">
              <CardTitle className="flex items-center gap-3 text-2xl">
                <div
                  className="p-2 rounded-xl"
                  style={{
                    backgroundColor: `${foodChain.theme.primaryColor}20`,
                  }}
                >
                  <Star
                    className="w-6 h-6"
                    style={{ color: foodChain.theme.primaryColor }}
                  />
                </div>
                <span>Featured Dishes</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {featuredDishes.slice(0, 4).map((dish) => (
                  <Card
                    key={dish._id}
                    className="border-2 border-yellow-200 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden group"
                  >
                    <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200">
                      {dish.image ? (
                        <Image
                          src={dish.image}
                          alt={dish.name}
                          fill
                          className="object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <ChefHat className="w-12 h-12 text-gray-300" />
                        </div>
                      )}
                      <Badge className="absolute top-3 left-3 bg-yellow-500">
                        <Star className="w-3 h-3 mr-1 fill-white" />
                        Featured
                      </Badge>
                    </div>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-bold text-lg">{dish.name}</h3>
                        {config.showPricing !== false && (
                          <span
                            className="font-bold text-lg"
                            style={{ color: foodChain.theme.primaryColor }}
                          >
                            ₹{dish.price}
                          </span>
                        )}
                      </div>
                      {dish.description && (
                        <p className="text-gray-600 text-sm line-clamp-2">
                          {dish.description}
                        </p>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Menu Section */}
        {config.enableMenuSection !== false && dishes && dishes.length > 0 && (
          <Card className="mb-12 border-0 shadow-lg overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-white border-b">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <div
                    className="p-2 rounded-xl"
                    style={{
                      backgroundColor: `${foodChain.theme.primaryColor}20`,
                    }}
                  >
                    <ChefHat
                      className="w-6 h-6"
                      style={{ color: foodChain.theme.primaryColor }}
                    />
                  </div>
                  <span>Our Menu</span>
                </CardTitle>

                {/* Search Bar */}
                {config.pageConfig?.showSearchBar !== false && (
                  <div className="relative w-full lg:w-96">
                    <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <Input
                      onChange={(e) => setSearch(e.target.value)}
                      className="pl-12 pr-12 h-12 rounded-xl border-2 border-gray-200 focus:border-gray-300 bg-white"
                      placeholder="Search for dishes..."
                      value={search}
                    />
                    {search && (
                      <button
                        onClick={() => setSearch("")}
                        className="absolute right-3 top-1/2 -translate-y-1/2 p-1 hover:bg-gray-100 rounded-lg transition-colors"
                      >
                        <X className="w-5 h-5 text-gray-400" />
                      </button>
                    )}
                  </div>
                )}
              </div>

              {/* Category Filter */}
              {config.pageConfig?.showCategories !== false && (
                <div className="flex flex-wrap gap-2 mt-6 pt-6 border-t">
                  <Button
                    variant={selectedCategory === "all" ? "default" : "outline"}
                    size="lg"
                    onClick={() => setSelectedCategory("all")}
                    className="rounded-full font-semibold transition-all duration-300"
                    style={
                      selectedCategory === "all"
                        ? { backgroundColor: foodChain.theme.primaryColor }
                        : {}
                    }
                  >
                    All Items
                  </Button>
                  {foodChain.categories.map((category) => (
                    <Button
                      key={category._id}
                      variant={
                        selectedCategory === category._id
                          ? "default"
                          : "outline"
                      }
                      size="lg"
                      onClick={() => setSelectedCategory(category._id)}
                      className="rounded-full font-semibold transition-all duration-300"
                      style={
                        selectedCategory === category._id
                          ? { backgroundColor: foodChain.theme.primaryColor }
                          : {}
                      }
                    >
                      {category.name}
                    </Button>
                  ))}
                </div>
              )}
            </CardHeader>

            <CardContent className="p-8">
              {filteredDishes.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredDishes.map((dish) => (
                    <Card
                      key={dish._id}
                      className="border-2 border-gray-100 hover:border-gray-200 shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden group"
                    >
                      <div className="relative h-56 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
                        {dish.image ? (
                          <>
                            <Image
                              src={dish.image}
                              alt={dish.name}
                              fill
                              className="object-cover group-hover:scale-110 transition-transform duration-500"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          </>
                        ) : (
                          <div className="flex items-center justify-center h-full">
                            <ChefHat className="w-16 h-16 text-gray-300" />
                          </div>
                        )}

                        <div className="absolute top-3 left-3 flex flex-col gap-2">
                          {dish.isFeatured && (
                            <Badge className="bg-yellow-500 hover:bg-yellow-600 shadow-lg">
                              <Star className="w-3 h-3 mr-1 fill-white" />
                              Featured
                            </Badge>
                          )}
                          {!dish.isAvailable && (
                            <Badge variant="destructive" className="shadow-lg">
                              Out of Stock
                            </Badge>
                          )}
                        </div>

                        <button
                          onClick={() => toggleFavorite(dish._id)}
                          className="absolute top-3 right-3 bg-white/90 hover:bg-white p-2 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110"
                        >
                          <Heart
                            className={`w-5 h-5 transition-colors ${
                              favorites.has(dish._id)
                                ? "fill-red-500 text-red-500"
                                : "text-gray-600"
                            }`}
                          />
                        </button>
                      </div>

                      <CardContent className="p-6">
                        <div className="flex justify-between items-start mb-3">
                          <h3 className="font-bold text-xl text-gray-900 flex-1 pr-2">
                            {dish.name}
                          </h3>
                          {config.showPricing !== false && (
                            <span
                              className="font-bold text-xl whitespace-nowrap"
                              style={{ color: foodChain.theme.primaryColor }}
                            >
                              ₹{dish.price}
                            </span>
                          )}
                        </div>

                        {dish.description && (
                          <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                            {dish.description}
                          </p>
                        )}

                        <div className="flex items-center justify-between flex-wrap gap-2">
                          <div className="flex items-center gap-2">
                            <Badge
                              variant="outline"
                              className="text-xs font-medium border-2"
                            >
                              {stringReducer(dish.category.name, 15)}
                            </Badge>
                            <Badge
                              className={`text-xs font-semibold ${
                                dish.isVeg
                                  ? "bg-green-100 text-green-700 hover:bg-green-200"
                                  : "bg-red-100 text-red-700 hover:bg-red-200"
                              }`}
                            >
                              {dish.isVeg ? "🟢 Veg" : "🔴 Non-Veg"}
                            </Badge>
                          </div>
                          {config.showRatings !== false && dish.rating && (
                            <div className="flex items-center gap-1 bg-yellow-50 px-2 py-1 rounded-lg">
                              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                              <span className="text-sm font-bold text-gray-900">
                                {dish.rating}
                              </span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-16">
                  <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Search className="w-12 h-12 text-gray-400" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    No dishes found
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Try adjusting your search or filter to find what you&apos;re
                    looking for
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearch("");
                      setSelectedCategory("all");
                    }}
                    size="lg"
                    className="rounded-full"
                  >
                    Clear Filters
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Gallery Section */}
        {config.enableGallerySection &&
          foodChain.gallery?.enabled &&
          foodChain.gallery.images && (
            <Card className="mb-12 border-0 shadow-lg overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-white border-b">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <div
                    className="p-2 rounded-xl"
                    style={{
                      backgroundColor: `${foodChain.theme.primaryColor}20`,
                    }}
                  >
                    <Award
                      className="w-6 h-6"
                      style={{ color: foodChain.theme.primaryColor }}
                    />
                  </div>
                  <span>{foodChain.gallery.title || "Gallery"}</span>
                </CardTitle>
                {galleryCategories.length > 1 && (
                  <div className="flex flex-wrap gap-2 mt-4">
                    {galleryCategories.map((cat) => (
                      <Button
                        key={cat}
                        variant={
                          selectedGalleryCategory === cat
                            ? "default"
                            : "outline"
                        }
                        size="sm"
                        onClick={() =>
                          setSelectedGalleryCategory(cat as string)
                        }
                        style={
                          selectedGalleryCategory === cat
                            ? { backgroundColor: foodChain.theme.primaryColor }
                            : {}
                        }
                      >
                        {cat === "all" ? "All" : cat}
                      </Button>
                    ))}
                  </div>
                )}
              </CardHeader>
              <CardContent className="p-8">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {filteredGalleryImages.map((img, index) => (
                    <div
                      key={index}
                      className="relative aspect-square rounded-xl overflow-hidden group"
                    >
                      <Image
                        src={img.url}
                        alt={img.caption || `Gallery ${index + 1}`}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                      {img.caption && (
                        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-end p-4">
                          <p className="text-white text-sm">{img.caption}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

        {/* Testimonials Section */}
        {config.enableTestimonialsSection &&
          foodChain.testimonials?.enabled &&
          foodChain.testimonials.reviews &&
          foodChain.testimonials.reviews.length > 0 && (
            <Card className="mb-12 border-0 shadow-lg overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-white border-b">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <div
                    className="p-2 rounded-xl"
                    style={{
                      backgroundColor: `${foodChain.theme.primaryColor}20`,
                    }}
                  >
                    <Star
                      className="w-6 h-6"
                      style={{ color: foodChain.theme.primaryColor }}
                    />
                  </div>
                  <span>
                    {foodChain.testimonials.title || "What Our Customers Say"}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {foodChain.testimonials.reviews
                    .slice(0, 6)
                    .map((review, index) => (
                      <Card
                        key={index}
                        className="border-2 border-gray-100 p-6"
                      >
                        <div className="flex items-center gap-4 mb-4">
                          {review.customerImage ? (
                            <Avatar className="size-12">
                              <AvatarImage
                                src={review.customerImage}
                                alt={review.customerName}
                              />
                              <AvatarFallback>
                                {review.customerName[0]}
                              </AvatarFallback>
                            </Avatar>
                          ) : (
                            <div className="size-12 rounded-full bg-gray-200 flex items-center justify-center">
                              <span className="text-gray-600 font-semibold">
                                {review.customerName[0]}
                              </span>
                            </div>
                          )}
                          <div>
                            <p className="font-semibold text-gray-900">
                              {review.customerName}
                            </p>
                            <div className="flex items-center gap-1">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`w-4 h-4 ${
                                    i < review.rating
                                      ? "fill-yellow-400 text-yellow-400"
                                      : "text-gray-300"
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                        <p className="text-gray-600 text-sm italic">
                          &quot;{review.review}&quot;
                        </p>
                      </Card>
                    ))}
                </div>
              </CardContent>
            </Card>
          )}

        {/* FAQ Section */}
        {config.enableFAQSection &&
          foodChain.faqs &&
          foodChain.faqs.length > 0 && (
            <Card className="mb-12 border-0 shadow-lg overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-white border-b">
                <CardTitle className="flex items-center gap-3 text-2xl">
                  <div
                    className="p-2 rounded-xl"
                    style={{
                      backgroundColor: `${foodChain.theme.primaryColor}20`,
                    }}
                  >
                    <Info
                      className="w-6 h-6"
                      style={{ color: foodChain.theme.primaryColor }}
                    />
                  </div>
                  <span>Frequently Asked Questions</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <Accordion type="single" collapsible className="w-full">
                  {foodChain.faqs
                    .sort((a, b) => a.order - b.order)
                    .map((faq, index) => (
                      <AccordionItem key={index} value={`item-${index}`}>
                        <AccordionTrigger className="text-left font-semibold">
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-gray-600">
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                </Accordion>
              </CardContent>
            </Card>
          )}

        {/* CTA Section */}
        <Card className="border-0 shadow-2xl overflow-hidden">
          <div
            className="relative bg-gradient-to-br p-12 text-white"
            style={{
              background: `linear-gradient(135deg, ${foodChain.theme.primaryColor}, ${foodChain.theme.secondaryColor})`,
            }}
          >
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 right-0 w-64 h-64 bg-white rounded-full filter blur-3xl" />
              <div className="absolute bottom-0 left-0 w-64 h-64 bg-white rounded-full filter blur-3xl" />
            </div>

            <CardContent className="relative p-0 text-center">
              <div className="max-w-3xl mx-auto">
                <h2 className="text-4xl md:text-5xl font-bold mb-4 drop-shadow-lg">
                  Ready to Order?
                </h2>
                <p className="text-xl text-white/95 mb-10 drop-shadow-md">
                  Chat with any of our outlets to place your order and get
                  personalized recommendations!
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  {foodChain.outlets.slice(0, 2).map((outlet) => (
                    <Button
                      key={outlet._id}
                      onClick={() => handleChatWithOutlet(outlet._id)}
                      size="lg"
                      className="bg-white text-gray-900 hover:bg-gray-100 font-bold text-lg px-8 py-6 rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
                    >
                      <MessageCircle className="w-6 h-6 mr-3" />
                      <div className="text-left">
                        <div>{outlet.name}</div>
                        <div className="text-xs font-normal text-gray-600">
                          {stringReducer(outlet.address, 30)}
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </div>
        </Card>
      </div>

      {/* Custom Styles */}
      <style jsx global>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in {
          animation: fade-in 0.6s ease-out;
        }

        @keyframes pulse {
          0%,
          100% {
            opacity: 0.1;
          }
          50% {
            opacity: 0.2;
          }
        }

        .animate-pulse {
          animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .delay-1000 {
          animation-delay: 1s;
        }
      `}</style>
    </div>
  );
};

export default FullFeaturedFoodChainPage;
