import User from "../models/User.js";
import FoodChain from "../models/FoodChain.js";
import FoodChainRegistrationRequest from "../models/FoodChainRegistrationRequest.js";
import Outlet from "../models/Outlet.js";
import Order from "../models/Order.js";
import Payment from "../models/Payment.js";
import bcrypt from "bcrypt";
import { getValidOutletIdsForFoodChain } from "./admin-controller.js";
export const createSuperAdmin = async (req, res) => {
  try {
    const { email, password, name } = req.body;
    if (!email || !password || !name) {
      return res
        .status(400)
        .json({ message: "Email, password and name are required" });
    }
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: "User already exists" });
    }
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    const user = new User({
      email,
      password: hashedPassword,
      name,
      role: "super_admin",
    });
    await user.save();
    res.status(201).json({
      success: true,
      message: "Super admin created successfully",
      data: {
        email: user.email,
        name: user.name,
        role: user.role,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating super admin",
      error: error.message,
    });
  }
};

export const createFoodChain = async (req, res) => {
  try {
    const { name, contact, theme = {}, tagline = "", email = "" } = req.body;

    // Validate required fields
    if (!name || !contact) {
      return res.status(400).json({ message: "Name and contact are required" });
    }

    // Create new food chain
    const foodChain = new FoodChain({
      name,
      contact,
      theme,
      tagline,
      email,
    });

    await foodChain.save();

    res.status(201).json({
      success: true,
      message: "Food chain created successfully",
      data: foodChain,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating food chain",
      error: error.message,
    });
  }
};

export const registerFoodChainAdmin = async (req, res) => {
  try {
    const { email, password, name, foodChainId, phone } = req.body;

    // Validate required fields
    if (!email || !password || !name || !foodChainId) {
      return res.status(400).json({
        message: "Email, password, name and foodChainId are required",
      });
    }

    // Check if food chain exists
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({ message: "Food chain not found" });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: "User already exists" });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create new admin user
    const user = new User({
      email,
      password: hashedPassword,
      name,
      phone,
      foodChain: foodChainId,
      role: "admin",
    });

    await user.save();

    res.status(201).json({
      success: true,
      message: "Food chain admin registered successfully",
      data: {
        email: user.email,
        name: user.name,
        foodChain: user.foodChain,
        role: user.role,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error registering food chain admin",
      error: error.message,
    });
  }
};

export const getAllFoodChains = async (req, res) => {
  try {
    const foodChains = await FoodChain.find()
      .select("-__v")
      .populate("outlets", "name address contact");

    res.status(200).json({
      success: true,
      data: foodChains,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching food chains",
      error: error.message,
    });
  }
};

export const getFoodChainById = async (req, res) => {
  try {
    const { id } = req.params;
    const validOutletIds = await getValidOutletIdsForFoodChain(id);
    let foodChain = await FoodChain.findById(id)
      .select("-__v")
      .populate("outlets", "name address contact")
      .populate("categories", "name description");

    const [currentPeriodStats] = await Promise.all([
      // Current period stats (last 30 days)
      Order.aggregate([
        {
          $match: {
            outletId: { $in: validOutletIds },
            status: "completed",
          },
        },
        {
          $group: {
            _id: null,
            totalOrders: { $sum: 1 },
            totalRevenue: { $sum: "$totalAmount" },
            averageOrderValue: { $avg: "$totalAmount" },
            uniqueCustomers: { $addToSet: "$userId" },
          },
        },
      ]),
    ]);

    if (!foodChain) {
      return res.status(404).json({ message: "Food chain not found" });
    }

    res.status(200).json({
      success: true,
      data: foodChain,
      customers: currentPeriodStats[0]?.uniqueCustomers?.length,
      totalOrders: currentPeriodStats[0]?.totalOrders,
      totalRevenue: currentPeriodStats[0]?.totalRevenue,
      averageOrderValue: currentPeriodStats[0]?.averageOrderValue,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching food chain",
      error: error.message,
    });
  }
};

export const updateFoodChain = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      contact,
      theme,
      email,
      website,
      tagline,
      status,
      businessType,
      subcategory,
      contactPerson,
      phone,
      address,
      legalInfo,
      bankAccountInfo,
      aboutSection,
      analytics,
      gallery,
      features,
      heroSection,
      pageConfig,
      customization,
      loyaltyProgram,
      operatingHours,
      socialMedia,
      testimonials,
      announcements,
      faqs,
      promotions,
      seo,

    } = req.body;

    const foodChain = await FoodChain.findById(id);
    if (!foodChain) {
      return res.status(404).json({ message: "Food chain not found" });
    }

    // Update basic fields
    if (name) foodChain.name = name;
    if (contact) foodChain.contact = contact;
    if (theme) foodChain.theme = theme;
    if (email) foodChain.email = email;
    if (website) foodChain.website = website;
    if (tagline) foodChain.tagline = tagline;
    if (status && ["active", "inactive", "suspended"].includes(status))
      foodChain.status = status;

    // Update business details
    if (businessType) foodChain.businessType = businessType;
    if (subcategory) foodChain.subcategory = subcategory;
    if (contactPerson) foodChain.contactPerson = contactPerson;
    if (phone) foodChain.phone = phone;
    if (aboutSection) foodChain.aboutSection = aboutSection;
    if (analytics) foodChain.analytics = analytics;
    if (gallery) foodChain.gallery = gallery;
    if (features) foodChain.features = features;
    if (heroSection) foodChain.heroSection = heroSection;
    if (pageConfig) foodChain.pageConfig = pageConfig;
    if (customization) foodChain.customization = customization;
    if (loyaltyProgram) foodChain.loyaltyProgram = loyaltyProgram;
    if (operatingHours) foodChain.operatingHours = operatingHours;
    if (socialMedia) foodChain.socialMedia = socialMedia;
    if (testimonials) foodChain.testimonials = testimonials;
    if (announcements) foodChain.announcements = announcements;
    if (seo) foodChain.seo = seo;
    if (faqs) foodChain.faqs = faqs;
    if (promotions) foodChain.promotions = promotions;

    // Update address if provided
    if (address) {
      foodChain.address = {
        ...foodChain.address,
        ...address,
      };
    }

    // Update legal info if provided
    if (legalInfo) {
      foodChain.legalInfo = {
        ...foodChain.legalInfo,
        ...legalInfo,
      };
    }

    // Update bank account info if provided
    if (bankAccountInfo) {
      foodChain.bankAccountInfo = {
        ...foodChain.bankAccountInfo,
        ...bankAccountInfo,
      };
    }

    foodChain.updatedAt = Date.now();
    await foodChain.save();

    res.status(200).json({
      success: true,
      message: "Food chain updated successfully",
      data: foodChain,
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      success: false,
      message: "Error updating food chain",
      error: error.message,
    });
  }
};

export const updateFoodChainStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // Validate status
    if (!status || !["active", "inactive", "suspended"].includes(status)) {
      return res.status(400).json({
        success: false,
        message:
          "Invalid status. Status must be 'active', 'inactive', or 'suspended'",
      });
    }

    const foodChain = await FoodChain.findById(id);
    if (!foodChain) {
      return res.status(404).json({ message: "Food chain not found" });
    }

    // Update status
    foodChain.status = status;
    foodChain.updatedAt = Date.now();
    await foodChain.save();

    // If chain is inactive or suspended, update all outlets to inactive
    if (status === "inactive" || status === "suspended") {
      await Outlet.updateMany({ foodChain: id }, { status: "inactive" });
    }

    res.status(200).json({
      success: true,
      message: `Food chain status updated to ${status} successfully`,
      data: foodChain,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating food chain status",
      error: error.message,
    });
  }
};

export const deleteFoodChain = async (req, res) => {
  try {
    const { id } = req.params;

    // Find and delete food chain
    const foodChain = await FoodChain.findById(id);
    if (!foodChain) {
      return res.status(404).json({ message: "Food chain not found" });
    }

    // Delete associated admins
    await User.deleteMany({ foodChain: id, role: "admin" });

    // Delete the food chain
    await FoodChain.findByIdAndDelete(id);

    res.status(200).json({
      success: true,
      message: "Food chain and associated data deleted successfully",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error deleting food chain",
      error: error.message,
    });
  }
};

export const getFoodChainAdmins = async (req, res) => {
  try {
    const { foodChainId } = req.params;

    // Check if food chain exists
    const foodChain = await FoodChain.findById(foodChainId);
    if (!foodChain) {
      return res.status(404).json({ message: "Food chain not found" });
    }

    // Get all admins for the food chain
    const admins = await User.find({
      foodChain: foodChainId,
      role: "admin",
    }).select("-password -__v");

    res.status(200).json({
      success: true,
      data: admins,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching food chain admins",
      error: error.message,
    });
  }
};

// Super Admin Dashboard
export const getSuperAdminDashboard = async (req, res) => {
  try {
    // Get counts
    const totalFoodChains = await FoodChain.countDocuments();
    const activeFoodChains = await FoodChain.countDocuments({
      status: "active",
    });
    const inactiveFoodChains = await FoodChain.countDocuments({
      status: "inactive",
    });
    const suspendedFoodChains = await FoodChain.countDocuments({
      status: "suspended",
    });

    const totalOutlets = await Outlet.countDocuments();
    const activeOutlets = await Outlet.countDocuments({ status: "active" });
    const inactiveOutlets = await Outlet.countDocuments({ status: "inactive" });

    const totalUsers = await User.countDocuments({
      role: { $ne: "super-admin" },
    });
    const totalAdmins = await User.countDocuments({ role: "admin" });
    const totalCustomers = await User.countDocuments({ role: "user" });

    // Get recent orders
    const recentOrders = await Order.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .populate("outletId", "name")
      .populate("userId", "name");

    // Get recent payments
    const recentPayments = await Payment.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .populate("orderId", "orderNumber");

    // Get recent food chains
    const recentFoodChains = await FoodChain.find()
      .sort({ createdAt: -1 })
      .limit(5);

    // Get order stats
    const totalOrders = await Order.countDocuments();
    const pendingOrders = await Order.countDocuments({ status: "pending" });
    const completedOrders = await Order.countDocuments({ status: "completed" });
    const cancelledOrders = await Order.countDocuments({ status: "cancelled" });

    // Get payment stats
    const totalPayments = await Payment.countDocuments();
    const successfulPayments = await Payment.countDocuments({ status: "paid" });
    const failedPayments = await Payment.countDocuments({ status: "failed" });

    // Calculate total revenue
    const payments = await Payment.find({ status: "paid" });
    const totalRevenue = payments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    );

    res.status(200).json({
      success: true,
      data: {
        foodChains: {
          total: totalFoodChains,
          active: activeFoodChains,
          inactive: inactiveFoodChains,
          suspended: suspendedFoodChains,
          recent: recentFoodChains,
        },
        outlets: {
          total: totalOutlets,
          active: activeOutlets,
          inactive: inactiveOutlets,
        },
        users: {
          total: totalUsers,
          admins: totalAdmins,
          customers: totalCustomers,
        },
        orders: {
          total: totalOrders,
          pending: pendingOrders,
          completed: completedOrders,
          cancelled: cancelledOrders,
          recent: recentOrders,
        },
        payments: {
          total: totalPayments,
          successful: successfulPayments,
          failed: failedPayments,
          totalRevenue: totalRevenue,
          recent: recentPayments,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching dashboard data",
      error: error.message,
    });
  }
};

// System Analytics
export const getSystemAnalytics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Parse dates or use defaults (last 30 days)
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate
      ? new Date(startDate)
      : new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Validate dates
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({
        success: false,
        message: "Invalid date format. Use YYYY-MM-DD format.",
      });
    }

    // Set end date to end of day
    end.setHours(23, 59, 59, 999);

    // Get orders in date range
    const orders = await Order.find({
      createdAt: { $gte: start, $lte: end },
    }).populate("outletId");

    // Get payments in date range
    const payments = await Payment.find({
      createdAt: { $gte: start, $lte: end },
      status: "paid",
    });

    // Group orders by date
    const ordersByDate = {};
    const ordersByFoodChain = {};
    const ordersByOutlet = {};
    const ordersByStatus = {
      pending: 0,
      confirmed: 0,
      preparing: 0,
      ready: 0,
      completed: 0,
      cancelled: 0,
      rejected: 0,
      modified: 0,
    };

    // Initialize dates
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split("T")[0];
      ordersByDate[dateStr] = 0;
    }

    // Process orders
    orders.forEach((order) => {
      // Group by date
      const dateStr = order.createdAt.toISOString().split("T")[0];
      ordersByDate[dateStr] = (ordersByDate[dateStr] || 0) + 1;

      // Group by food chain
      const foodChainId = order.outletId?.foodChain?.toString() || "unknown";
      ordersByFoodChain[foodChainId] =
        (ordersByFoodChain[foodChainId] || 0) + 1;

      // Group by outlet
      const outletId = order.outletId?._id?.toString() || "unknown";
      ordersByOutlet[outletId] = (ordersByOutlet[outletId] || 0) + 1;

      // Group by status
      ordersByStatus[order.status] = (ordersByStatus[order.status] || 0) + 1;
    });

    // Calculate revenue by date
    const revenueByDate = {};

    // Initialize dates for revenue
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split("T")[0];
      revenueByDate[dateStr] = 0;
    }

    // Process payments
    payments.forEach((payment) => {
      const dateStr = payment.createdAt.toISOString().split("T")[0];
      revenueByDate[dateStr] = (revenueByDate[dateStr] || 0) + payment.amount;
    });

    // Get food chain details for the IDs
    const foodChainIds = Object.keys(ordersByFoodChain).filter(
      (id) => id !== "unknown"
    );
    const foodChains = await FoodChain.find({ _id: { $in: foodChainIds } });
    const foodChainMap = {};
    foodChains.forEach((fc) => {
      foodChainMap[fc._id.toString()] = fc.name;
    });

    // Get outlet details for the IDs
    const outletIds = Object.keys(ordersByOutlet).filter(
      (id) => id !== "unknown"
    );
    const outlets = await Outlet.find({ _id: { $in: outletIds } });
    const outletMap = {};
    outlets.forEach((outlet) => {
      outletMap[outlet._id.toString()] = outlet.name;
    });

    // Format data for response
    const formattedOrdersByFoodChain = Object.entries(ordersByFoodChain).map(
      ([id, count]) => ({
        id,
        name: id === "unknown" ? "Unknown" : foodChainMap[id] || "Unknown",
        count,
      })
    );

    const formattedOrdersByOutlet = Object.entries(ordersByOutlet).map(
      ([id, count]) => ({
        id,
        name: id === "unknown" ? "Unknown" : outletMap[id] || "Unknown",
        count,
      })
    );

    // Calculate totals
    const totalOrders = orders.length;
    const totalRevenue = payments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    );
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    res.status(200).json({
      success: true,
      data: {
        dateRange: {
          start: start.toISOString().split("T")[0],
          end: end.toISOString().split("T")[0],
        },
        summary: {
          totalOrders,
          totalRevenue,
          averageOrderValue,
        },
        ordersByDate,
        revenueByDate,
        ordersByFoodChain: formattedOrdersByFoodChain,
        ordersByOutlet: formattedOrdersByOutlet,
        ordersByStatus,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching analytics data",
      error: error.message,
    });
  }
};

// Financial Reports
export const getFinancialReports = async (req, res) => {
  try {
    const { startDate, endDate, foodChainId } = req.query;

    // Parse dates or use defaults (current month)
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate
      ? new Date(startDate)
      : new Date(end.getFullYear(), end.getMonth(), 1);

    // Validate dates
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json({
        success: false,
        message: "Invalid date format. Use YYYY-MM-DD format.",
      });
    }

    // Set end date to end of day
    end.setHours(23, 59, 59, 999);

    // Build query
    const query = {
      createdAt: { $gte: start, $lte: end },
      status: "paid",
    };

    // Add food chain filter if provided
    if (foodChainId) {
      // First get all outlets for this food chain
      const outlets = await Outlet.find({ foodChain: foodChainId }).select(
        "_id"
      );
      const outletIds = outlets.map((outlet) => outlet._id);

      // Then get all orders for these outlets
      const orders = await Order.find({ outletId: { $in: outletIds } }).select(
        "_id"
      );
      const orderIds = orders.map((order) => order._id);

      // Add to query
      query.orderId = { $in: orderIds };
    }

    // Get payments
    const payments = await Payment.find(query).populate({
      path: "orderId",
      populate: {
        path: "outletId",
        populate: {
          path: "foodChain",
        },
      },
    });

    // Calculate totals
    const totalRevenue = payments.reduce(
      (sum, payment) => sum + payment.amount,
      0
    );

    // Calculate tax amounts (assuming 5% tax)
    const taxRate = 0.05;
    const taxAmount = totalRevenue * taxRate;
    const netRevenue = totalRevenue - taxAmount;

    // Group by food chain
    const revenueByFoodChain = {};
    payments.forEach((payment) => {
      const foodChain = payment.orderId?.outletId?.foodChain;
      if (foodChain) {
        const id = foodChain._id.toString();
        const name = foodChain.name;

        if (!revenueByFoodChain[id]) {
          revenueByFoodChain[id] = {
            id,
            name,
            revenue: 0,
            tax: 0,
            net: 0,
            count: 0,
          };
        }

        revenueByFoodChain[id].revenue += payment.amount;
        revenueByFoodChain[id].tax += payment.amount * taxRate;
        revenueByFoodChain[id].net += payment.amount * (1 - taxRate);
        revenueByFoodChain[id].count += 1;
      }
    });

    // Format for response
    const formattedRevenueByFoodChain = Object.values(revenueByFoodChain);

    // Group by payment method
    const revenueByPaymentMethod = {
      cash: 0,
      online: 0,
    };

    payments.forEach((payment) => {
      const method = payment.orderId?.paymentMethod || "online";
      revenueByPaymentMethod[method] += payment.amount;
    });

    // Group by month if date range spans multiple months
    const revenueByMonth = {};
    payments.forEach((payment) => {
      const date = new Date(payment.createdAt);
      const monthKey = `${date.getFullYear()}-${String(
        date.getMonth() + 1
      ).padStart(2, "0")}`;

      if (!revenueByMonth[monthKey]) {
        revenueByMonth[monthKey] = {
          month: monthKey,
          revenue: 0,
          tax: 0,
          net: 0,
          count: 0,
        };
      }

      revenueByMonth[monthKey].revenue += payment.amount;
      revenueByMonth[monthKey].tax += payment.amount * taxRate;
      revenueByMonth[monthKey].net += payment.amount * (1 - taxRate);
      revenueByMonth[monthKey].count += 1;
    });

    // Format for response
    const formattedRevenueByMonth = Object.values(revenueByMonth);

    res.status(200).json({
      success: true,
      data: {
        dateRange: {
          start: start.toISOString().split("T")[0],
          end: end.toISOString().split("T")[0],
        },
        summary: {
          totalRevenue,
          taxAmount,
          netRevenue,
          transactionCount: payments.length,
        },
        revenueByFoodChain: formattedRevenueByFoodChain,
        revenueByPaymentMethod,
        revenueByMonth: formattedRevenueByMonth,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error generating financial reports",
      error: error.message,
    });
  }
};

// Food Chain Registration Request Management
export const getAllRegistrationRequests = async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;

    // Build query
    const query = {};
    if (status && status !== "all") {
      query.status = status;
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get requests with pagination
    const requests = await FoodChainRegistrationRequest.find(query)
      .populate("reviewedBy", "name email")
      .populate("createdFoodChainId", "name status")
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const totalRequests = await FoodChainRegistrationRequest.countDocuments(
      query
    );
    const totalPages = Math.ceil(totalRequests / parseInt(limit));

    res.status(200).json({
      success: true,
      data: {
        requests,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalRequests,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching registration requests",
      error: error.message,
    });
  }
};

export const updateRegistrationRequestStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, adminNotes } = req.body;
    const adminId = req.user.userId;
    console.log(status, "the status message");
    // Validate status
    if (!["pending", "approved", "rejected", "contacted"].includes(status)) {
      return res.status(400).json({
        success: false,
        message:
          "Invalid status. Must be one of: pending, approved, rejected, contacted",
      });
    }

    // Find the request
    const request = await FoodChainRegistrationRequest.findById(id);
    if (!request) {
      return res.status(404).json({
        success: false,
        message: "Registration request not found",
      });
    }

    // Update the request
    request.status = status;
    request.adminNotes = adminNotes || request.adminNotes;
    request.reviewedBy = adminId;
    request.reviewedAt = new Date();

    await request.save();

    // Populate the response
    await request.populate("reviewedBy", "name email");
    await request.populate("createdFoodChainId", "name status");

    res.status(200).json({
      success: true,
      message: `Registration request ${status} successfully`,
      data: request,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating registration request",
      error: error.message,
    });
  }
};

export const createFoodChainFromRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const { tagline = "", theme = {} } = req.body;
    const adminId = req.user.userId;

    // Find the request
    const request = await FoodChainRegistrationRequest.findById(id);
    if (!request) {
      return res.status(404).json({
        success: false,
        message: "Registration request not found",
      });
    }

    // Check if request is approved
    if (request.status !== "approved") {
      return res.status(400).json({
        success: false,
        message: "Only approved requests can be converted to food chains",
      });
    }

    // Check if food chain already created
    if (request.createdFoodChainId) {
      return res.status(400).json({
        success: false,
        message: "Food chain already created for this request",
      });
    }

    // Create new food chain
    const foodChain = new FoodChain({
      name: request.businessName,
      tagline: tagline || `Welcome to ${request.businessName}`,
      contact: request.phone,
      email: request.email,
      website: request.website,
      businessType: request.businessType,
      subcategory: request.subcategory,
      contactPerson: request.contactPersonName,
      phone: request.phone,
      address: {
        street: request.address,
        city: request.city,
        state: request.state,
        country: "IN",
      },
      theme: {
        primaryColor: theme.primaryColor || "#000000",
        secondaryColor: theme.secondaryColor || "#000000",
        accentColor: theme.accentColor || "#000000",
        logoUrl: theme.logoUrl || "",
        favIcon: theme.favIcon || "",
      },
    });

    await foodChain.save();

    // Update the request with the created food chain ID
    request.createdFoodChainId = foodChain._id;
    request.reviewedBy = adminId;
    request.reviewedAt = new Date();
    await request.save();

    res.status(201).json({
      success: true,
      message: "Food chain created successfully from registration request",
      data: {
        foodChain,
        request,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating food chain from request",
      error: error.message,
    });
  }
};
