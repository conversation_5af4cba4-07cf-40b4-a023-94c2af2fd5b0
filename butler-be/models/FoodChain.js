import mongoose from "mongoose";

const foodChainSchema = new mongoose.Schema({
  name: { type: String, required: true },
  tagline: { type: String, required: true },
  description: { type: String },
  contact: { type: String, required: true },
  email: { type: String, required: true },
  website: { type: String },
  status: {
    type: String,
    enum: ["active", "inactive", "suspended"],
    default: "active",
  },
  outlets: [{ type: mongoose.Schema.Types.ObjectId, ref: "Outlet" }],
  categories: [{ type: mongoose.Schema.Types.ObjectId, ref: "Category" }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  theme: {
    primaryColor: { type: String, default: "#000000" },
    secondaryColor: { type: String, default: "#000000" },
    accentColor: { type: String, default: "#000000" },
    logoUrl: { type: String, default: "" },
    favIcon: { type: String, default: "" },
    bannerImage: { type: String }, // Hero banner image
    fontFamily: { type: String, default: "Inter" },
  },
  bankDetails: {
    accountName: { type: String },
    accountNumber: { type: String },
    ifscCode: { type: String },
    bankName: { type: String },
    branchName: { type: String },
    upiId: { type: String },
  },
  // Razorpay Fund Account (old method)
  razorpayFundAccountId: { type: String },
  razorpayContactId: { type: String },
  razorpayVirtualAccountId: { type: String },

  // Razorpay Route (new method)
  razorpayAccountId: { type: String },
  razorpayAccountStatus: {
    type: String,
    enum: [
      "created",
      "activated",
      "needs_clarification",
      "rejected",
      "suspended",
    ],
    default: "created",
  },
  razorpayRouteEnabled: { type: Boolean, default: false },
  razorpayStakeholders: [{ type: String }],
  businessType: {
    type: String,
    enum: [
      "individual",
      "proprietorship",
      "partnership",
      "llp",
      "private_limited",
      "public_limited",
      "trust",
      "society",
      "ngo",
      "educational_institutes",
      "not_yet_registered",
      "other",
    ],
    default: "individual",
  },
  subcategory: {
    type: String,
    enum: [
      "restaurant",
      "online_food_ordering",
      "food_court",
      "catering",
      "alcohol",
      "restaurant_search_and_booking",
      "dairy_products",
      "bakeries",
    ],
    default: "restaurant",
  },
  contactPerson: { type: String },
  phone: { type: String },
  address: {
    street: { type: String },
    street2: { type: String },
    city: { type: String },
    state: { type: String },
    postalCode: { type: String },
    country: { type: String, default: "IN" },
  },
  legalInfo: {
    pan: { type: String },
    gst: { type: String },
  },
  bankAccountInfo: {
    name: { type: String },
    number: { type: String },
    ifsc: { type: String },
  },
  pageConfig: {
    // Feature Toggles
    enableHeroSection: { type: Boolean, default: true },
    enableAboutSection: { type: Boolean, default: true },
    enableOutletsSection: { type: Boolean, default: true },
    enableMenuSection: { type: Boolean, default: true },
    enableTestimonialsSection: { type: Boolean, default: false },
    enableGallerySection: { type: Boolean, default: false },
    enableFeaturedDishes: { type: Boolean, default: true },
    enableNewsSection: { type: Boolean, default: false },
    enableFAQSection: { type: Boolean, default: false },
    enableContactForm: { type: Boolean, default: false },
    enableSocialLinks: { type: Boolean, default: true },
    enableReservations: { type: Boolean, default: false },
    enableLoyaltyProgram: { type: Boolean, default: false },

    // Display Options
    showStats: { type: Boolean, default: true },
    showRatings: { type: Boolean, default: true },
    showPricing: { type: Boolean, default: true },
    showCategories: { type: Boolean, default: true },
    showSearchBar: { type: Boolean, default: true },

    // Layout Options
    heroStyle: {
      type: String,
      enum: ["gradient", "image", "video", "minimal"],
      default: "gradient"
    },
    menuLayout: {
      type: String,
      enum: ["grid", "list", "masonry"],
      default: "grid"
    },
  },
  heroSection: {
    enabled: { type: Boolean, default: true },
    title: { type: String }, // Custom hero title (fallback to name)
    subtitle: { type: String }, // Custom hero subtitle (fallback to tagline)
    backgroundImage: { type: String },
    backgroundVideo: { type: String },
    ctaText: { type: String, default: "Order Now" },
    ctaLink: { type: String },
    overlayOpacity: { type: Number, default: 0.3, min: 0, max: 1 },
  },

  // About Section
  aboutSection: {
    enabled: { type: Boolean, default: true },
    title: { type: String, default: "About Us" },
    content: { type: String }, // Rich text/HTML content
    images: [{ type: String }], // Multiple images for about section
    highlights: [
      {
        icon: { type: String }, // Icon name or URL
        title: { type: String },
        description: { type: String },
      },
    ],
  },

  // Gallery Section
  gallery: {
    enabled: { type: Boolean, default: false },
    title: { type: String, default: "Gallery" },
    images: [
      {
        url: { type: String },
        caption: { type: String },
        category: { type: String }, // "food", "interior", "events", etc.
      },
    ],
  },

  // Testimonials Section
  testimonials: {
    enabled: { type: Boolean, default: false },
    title: { type: String, default: "What Our Customers Say" },
    reviews: [
      {
        customerName: { type: String },
        customerImage: { type: String },
        rating: { type: Number, min: 1, max: 5 },
        review: { type: String },
        date: { type: Date },
        featured: { type: Boolean, default: false },
      },
    ],
  },

  // Announcements & News
  announcements: [
    {
      title: { type: String },
      content: { type: String },
      type: {
        type: String,
        enum: ["promotion", "event", "news", "alert"],
        default: "news"
      },
      image: { type: String },
      startDate: { type: Date },
      endDate: { type: Date },
      isActive: { type: Boolean, default: true },
      priority: { type: Number, default: 0 }, // Higher = shown first
    },
  ],

  // Special Offers & Promotions
  promotions: [
    {
      title: { type: String },
      description: { type: String },
      code: { type: String }, // Promo code
      discountType: {
        type: String,
        enum: ["percentage", "fixed"],
        default: "percentage"
      },
      discountValue: { type: Number },
      image: { type: String },
      startDate: { type: Date },
      endDate: { type: Date },
      isActive: { type: Boolean, default: true },
      applicableOutlets: [{ type: mongoose.Schema.Types.ObjectId, ref: "Outlet" }],
    },
  ],

  // FAQ Section
  faqs: [
    {
      question: { type: String },
      answer: { type: String },
      category: { type: String },
      order: { type: Number, default: 0 },
    },
  ],

  // Social Media Links
  socialMedia: {
    facebook: { type: String },
    instagram: { type: String },
    twitter: { type: String },
    youtube: { type: String },
    linkedin: { type: String },
    tiktok: { type: String },
    whatsapp: { type: String },
  },

  // Operating Hours (General)
  operatingHours: [
    {
      day: {
        type: String,
        enum: ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"],
      },
      isOpen: { type: Boolean, default: true },
      openTime: { type: String }, // "09:00"
      closeTime: { type: String }, // "22:00"
      breakStart: { type: String }, // Optional break time
      breakEnd: { type: String },
    },
  ],

  // Special Features
  features: {
    // Delivery & Ordering
    onlineOrdering: { type: Boolean, default: true },
    tableReservation: { type: Boolean, default: false },
    homeDelivery: { type: Boolean, default: true },
    takeaway: { type: Boolean, default: true },
    dineIn: { type: Boolean, default: true },

    // Services
    catering: { type: Boolean, default: false },
    privateEvents: { type: Boolean, default: false },
    corporateOrders: { type: Boolean, default: false },

    // Dietary Options
    vegetarianOptions: { type: Boolean, default: true },
    veganOptions: { type: Boolean, default: false },
    glutenFreeOptions: { type: Boolean, default: false },
    halalCertified: { type: Boolean, default: false },

    // Amenities
    freeWifi: { type: Boolean, default: false },
    parking: { type: Boolean, default: false },
    outdoorSeating: { type: Boolean, default: false },
    liveMusic: { type: Boolean, default: false },
    kidsPlay: { type: Boolean, default: false },
  },

  // Loyalty Program
  loyaltyProgram: {
    enabled: { type: Boolean, default: false },
    name: { type: String },
    description: { type: String },
    pointsPerRupee: { type: Number, default: 1 },
    benefits: [
      {
        title: { type: String },
        description: { type: String },
        pointsRequired: { type: Number },
      },
    ],
  },

  // SEO & Metadata
  seo: {
    metaTitle: { type: String },
    metaDescription: { type: String },
    metaKeywords: [{ type: String }],
    ogImage: { type: String }, // Open Graph image for social sharing
    canonicalUrl: { type: String },
  },

  // Custom CSS/JS (Advanced)
  customization: {
    customCSS: { type: String }, // Custom CSS for advanced users
    customJS: { type: String }, // Custom JavaScript
    headScripts: { type: String }, // Scripts to add in <head>
    bodyScripts: { type: String }, // Scripts to add before </body>
  },

  // Analytics & Tracking
  analytics: {
    googleAnalyticsId: { type: String },
    facebookPixelId: { type: String },
    googleTagManagerId: { type: String },
  },
});

export default mongoose.model("FoodChain", foodChainSchema);
